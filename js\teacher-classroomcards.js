// Import Firebase modules
import { collection, addDoc, deleteDoc, doc, updateDoc, getDocs, query, where } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js";

// Use the globally initialized Firebase instance
let db;

// Wait for the Firebase instance to be available
function getFirestoreInstance() {
    return new Promise((resolve, reject) => {
        // Check if db is already available in the window object
        if (window.db) {
            console.log("Using existing Firestore instance from window object in teacher-classroomcards.js");
            resolve(window.db);
            return;
        }

        // If not available, wait for it with a timeout
        let attempts = 0;
        const maxAttempts = 10;
        const checkInterval = setInterval(() => {
            attempts++;
            if (window.db) {
                clearInterval(checkInterval);
                console.log("Firestore instance found after waiting in teacher-classroomcards.js");
                resolve(window.db);
            } else if (attempts >= maxAttempts) {
                clearInterval(checkInterval);
                reject(new Error("Firestore instance not available after maximum attempts"));
            }
        }, 300);
    });
}

// Initialize db when the module loads
getFirestoreInstance()
    .then(firestoreInstance => {
        db = firestoreInstance;
        console.log("Firestore instance set in teacher-classroomcards.js");
    })
    .catch(error => {
        console.error("Error getting Firestore instance in teacher-classroomcards.js:", error);
    });

// Function to save a card's data to localStorage
function saveCardToLocalStorage(cardData) {
    try {
        let cards = JSON.parse(localStorage.getItem('teacherCards')) || [];
        cards.push(cardData);
        localStorage.setItem('teacherCards', JSON.stringify(cards));

        // Update managed classes count in profile
        updateManagedClassesCount();
    } catch (error) {
        console.error("Error saving to localStorage:", error);
    }
}

// Function to update the managed classes count in the profile
function updateManagedClassesCount() {
    const teacherCards = JSON.parse(localStorage.getItem('teacherCards')) || [];
    const countElement = document.getElementById('modalManagedClasses');
    if (countElement) {
        countElement.textContent = teacherCards.length;
    }
}

// Function to remove a card from localStorage
function removeCardFromLocalStorage(cardData) {
    try {
        let cards = JSON.parse(localStorage.getItem('teacherCards')) || [];
        cards = cards.filter(card => card.id !== cardData.id);
        localStorage.setItem('teacherCards', JSON.stringify(cards));

        // Update managed classes count in profile
        updateManagedClassesCount();
    } catch (error) {
        console.error("Error removing from localStorage:", error);
    }
}

// Function to update a card's data in localStorage
function updateCardInLocalStorage(cardData) {
    try {
        let cards = JSON.parse(localStorage.getItem('teacherCards')) || [];
        const index = cards.findIndex(card => card.id === cardData.id);
        if (index !== -1) {
            cards[index] = cardData;
            localStorage.setItem('teacherCards', JSON.stringify(cards));
        }
    } catch (error) {
        console.error("Error updating localStorage:", error);
    }
}

// Function to edit classroom details
function editClassroom(card) {
    console.log("Editing classroom for card:", card);

    // Get the text elements inside the card
    const subjectText = card.querySelector('.text-primary');
    const sectionText = card.querySelector('.text-gray-800');
    const detailsText = card.querySelector('.text-gray-600');

    // If text elements are missing, log an error
    if (!subjectText || !sectionText || !detailsText) {
        console.error("Error: Required elements not found in the card!");
        console.log("Card content:", card.innerHTML);
        return;
    }

    // Create input fields for editing
    const subjectInput = document.createElement('input');
    subjectInput.type = 'text';
    subjectInput.className = 'form-control mb-2 edit-input';
    subjectInput.value = subjectText.textContent;

    const sectionInput = document.createElement('input');
    sectionInput.type = 'text';
    sectionInput.className = 'form-control mb-2 edit-input';
    sectionInput.value = sectionText.textContent;

    const detailsInput = document.createElement('input');
    detailsInput.type = 'text';
    detailsInput.className = 'form-control mb-2 edit-input';
    detailsInput.value = detailsText.textContent;

    // Replace text with input fields
    subjectText.replaceWith(subjectInput);
    sectionText.replaceWith(sectionInput);
    detailsText.replaceWith(detailsInput);

    // Create save button
    const saveBtn = document.createElement('button');
    saveBtn.className = 'btn btn-sm btn-success mt-2';
    saveBtn.textContent = 'Save';

    // Create cancel button
    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'btn btn-sm btn-secondary mt-2 ml-2';
    cancelBtn.textContent = 'Cancel';

    // Handle save action
    saveBtn.addEventListener('click', async function () {
        console.log("Saving changes...");

        // Update the text content with the new values
        subjectText.textContent = subjectInput.value;
        sectionText.textContent = sectionInput.value;
        detailsText.textContent = detailsInput.value;

        // Replace input fields with updated text
        subjectInput.replaceWith(subjectText);
        sectionInput.replaceWith(sectionText);
        detailsInput.replaceWith(detailsText);

        // Remove buttons
        saveBtn.remove();
        cancelBtn.remove();

        // Update in both Firestore and localStorage
        const classroomId = card.dataset.classroomId;
        const updatedData = {
            subjectName: subjectInput.value,
            sectionName: sectionInput.value,
            details: detailsInput.value
        };

        try {
            // Update Firestore
            const classroomRef = doc(db, "Classrooms", classroomId);
            await updateDoc(classroomRef, updatedData);

            // Update localStorage
            const cardData = {
                id: classroomId,
                subjectName: subjectInput.value,
                sectionName: sectionInput.value,
                details: detailsInput.value
            };
            updateCardInLocalStorage(cardData);
        } catch (error) {
            console.error("Error updating classroom:", error);
            alert("Failed to update classroom. Please try again.");
        }
    });

    // Handle cancel action
    cancelBtn.addEventListener('click', function () {
        console.log("Edit cancelled.");
        subjectInput.replaceWith(subjectText);
        sectionInput.replaceWith(sectionText);
        detailsInput.replaceWith(detailsText);
        saveBtn.remove();
        cancelBtn.remove();
    });

    // Append buttons to the card body
    card.querySelector('.card-body').appendChild(saveBtn);
    card.querySelector('.card-body').appendChild(cancelBtn);
}

// Function to generate a random enrollment code
function generateEnrollCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let code = '';
    for (let i = 0; i < 5; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
}

// Function to create a classroom card element (returns the element)
function createCardElement(cardData) {
    const cardContainer = document.createElement('div');
    cardContainer.className = 'col-xl-3 col-md-6 mb-4';

    const card = document.createElement('div');
    card.className = 'card border-left-primary shadow h-100 py-2 position-relative clickable-classroom';
    card.style.borderLeft = `0.25rem solid ${cardData.color}`;
    card.dataset.classroomId = cardData.id; // Store the Firestore document ID
    card.style.cursor = 'pointer'; // Add pointer cursor to indicate clickability

    const cardBody = document.createElement('div');
    cardBody.className = 'card-body position-relative';

    // Menu button to toggle card options
    const menuBtn = document.createElement('button');
    menuBtn.className = 'menu-btn';
    menuBtn.innerHTML = '&#8942;';
    menuBtn.addEventListener('click', function (event) {
        event.stopPropagation();
        toggleMenu(menu);
    });

    // Card menu for actions like open, edit, and delete
    const menu = document.createElement('div');
    menu.className = 'card-menu hidden';

    const editBtn = document.createElement('button');
    editBtn.textContent = 'Edit';
    editBtn.className = 'edit-btn';
    editBtn.addEventListener('click', function (event) {
        event.stopPropagation(); // Prevent card click when editing
        editClassroom(card);
    });

    const deleteBtn = document.createElement('button');
    deleteBtn.textContent = 'Delete';
    deleteBtn.addEventListener('click', async function (event) {
        event.stopPropagation(); // Prevent card click when deleting
        try {
            // Delete from Firestore
            await deleteDoc(doc(db, "Classrooms", cardData.id));

            // Delete from localStorage
            removeCardFromLocalStorage(cardData);

            // Remove from UI
            cardContainer.remove();
        } catch (error) {
            console.error("Error deleting classroom:", error);
            alert("Failed to delete classroom. Please try again.");
        }
    });

    menu.appendChild(editBtn);
    menu.appendChild(deleteBtn);

    // Text content for the classroom card
    const textColumn = document.createElement('div');
    textColumn.className = 'col mr-2';

    const subjectText = document.createElement('div');
    subjectText.className = 'text-xs font-bold text-primary uppercase mb-1 classroom-card-text truncate-single subject-text';
    subjectText.textContent = cardData.subjectName;

    // Add tooltip if text is long
    if (cardData.subjectName && cardData.subjectName.length > 25) {
        subjectText.classList.add('text-tooltip');
        subjectText.setAttribute('data-full-text', cardData.subjectName);
        subjectText.title = cardData.subjectName;
    }

    // Large section text removed - only keeping the smaller section display below

    const detailsText = document.createElement('div');
    detailsText.className = 'text-sm mb-2 text-gray-600 classroom-card-text truncate details-text';
    detailsText.textContent = cardData.details;

    // Add tooltip if text is long
    if (cardData.details && cardData.details.length > 50) {
        detailsText.classList.add('text-tooltip');
        detailsText.setAttribute('data-full-text', cardData.details);
        detailsText.title = cardData.details;
    }

    const gradeLevelText = document.createElement('div');
    gradeLevelText.className = 'text-xs font-weight-bold text-secondary mt-2';
    gradeLevelText.textContent = `Grade Level: ${cardData.gradeLevel}`;

    const sectionDisplayText = document.createElement('div');
    sectionDisplayText.className = 'text-xs font-weight-bold text-secondary mt-1';
    sectionDisplayText.textContent = `Section: ${cardData.sectionName || 'No Section'}`;

    const courseText = document.createElement('div');
    courseText.className = 'text-xs font-weight-bold text-secondary mt-2';
    courseText.textContent = `Strand: ${cardData.course}`;

    const enrollCodeText = document.createElement('div');
    enrollCodeText.className = 'text-xs font-weight-bold text-secondary mt-2 d-flex align-items-center';

    const enrollCodeLabel = document.createElement('span');
    enrollCodeLabel.textContent = `Enroll Code: `;

    const enrollCodeValue = document.createElement('span');
    enrollCodeValue.className = 'font-weight-bold text-primary';
    enrollCodeValue.textContent = cardData.enrollCode;

    const copyButton = document.createElement('button');
    copyButton.className = 'btn btn-sm btn-link ml-2 p-0';
    copyButton.innerHTML = '<i class="fas fa-copy"></i>';
    copyButton.title = 'Copy enrollment code';
    copyButton.addEventListener('click', function(e) {
        e.stopPropagation();
        navigator.clipboard.writeText(cardData.enrollCode).then(() => {
            copyButton.innerHTML = '<i class="fas fa-check"></i>';
            setTimeout(() => {
                copyButton.innerHTML = '<i class="fas fa-copy"></i>';
            }, 2000);
        });
    });

    enrollCodeText.appendChild(enrollCodeLabel);
    enrollCodeText.appendChild(enrollCodeValue);
    enrollCodeText.appendChild(copyButton);

    textColumn.appendChild(subjectText);
    textColumn.appendChild(detailsText);
    textColumn.appendChild(gradeLevelText);
    textColumn.appendChild(sectionDisplayText);
    textColumn.appendChild(courseText);
    textColumn.appendChild(enrollCodeText);

    cardBody.appendChild(menuBtn);
    cardBody.appendChild(menu);
    cardBody.appendChild(textColumn);
    card.appendChild(cardBody);
    cardContainer.appendChild(card);

    // Add click event to navigate to classroom materials page
    card.addEventListener('click', function(e) {
        // Only redirect if clicking outside the dropdown menu and action buttons
        if (!e.target.closest('.card-menu') && !e.target.closest('.menu-btn') && !e.target.closest('.edit-btn') && !e.target.closest('.delete-btn')) {
            // Mark as internal navigation to prevent logout prompt
            if (window.isNavigatingWithinLMS !== undefined) {
                window.isNavigatingWithinLMS = true;
            }
            window.location.href = `classroom-materials.html?id=${cardData.id}`;
        }
    });

    return cardContainer;
}

// Function to create and display a new classroom card
function createCard(cardData) {
    const cardElement = createCardElement(cardData);
    document.getElementById('teacher-classroom-container').appendChild(cardElement);
}

// Function to toggle the visibility of the card menu
function toggleMenu(menu) {
    document.querySelectorAll('.card-menu').forEach(m => {
        if (m !== menu) m.classList.add('hidden');
    });
    menu.classList.toggle('hidden');
}

// Function to group classrooms based on the selected option
function groupClassrooms(classrooms, groupOption) {
    if (groupOption === 'none') {
        return { 'All Classrooms': classrooms };
    }

    const groups = {};

    classrooms.forEach(classroom => {
        let groupKey;

        switch (groupOption) {
            case 'subject':
                groupKey = classroom.subjectName || 'Unknown Subject';
                break;

            case 'grade':
                groupKey = classroom.gradeLevel || 'Unknown Grade';
                break;

            case 'course':
                groupKey = classroom.course || 'Unknown Strand';
                break;

            case 'section':
                groupKey = classroom.sectionName || 'Unknown Section';
                break;

            default:
                groupKey = 'All Classrooms';
        }

        if (!groups[groupKey]) {
            groups[groupKey] = [];
        }
        groups[groupKey].push(classroom);
    });

    return groups;
}

// Function to sort classrooms based on the selected option
function sortClassrooms(a, b, sortOption) {
    switch (sortOption) {
        case 'name-asc':
            const nameA = (a.sectionName ? `${a.subjectName} - ${a.sectionName}` : (a.subjectName || 'Unnamed Class')).toLowerCase();
            const nameB = (b.sectionName ? `${b.subjectName} - ${b.sectionName}` : (b.subjectName || 'Unnamed Class')).toLowerCase();
            return nameA.localeCompare(nameB);

        case 'name-desc':
            const nameA2 = (a.sectionName ? `${a.subjectName} - ${a.sectionName}` : (a.subjectName || 'Unnamed Class')).toLowerCase();
            const nameB2 = (b.sectionName ? `${b.subjectName} - ${b.sectionName}` : (b.subjectName || 'Unnamed Class')).toLowerCase();
            return nameB2.localeCompare(nameA2);

        case 'date-desc':
            const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt) || new Date(0);
            const dateB = b.createdAt?.toDate?.() || new Date(b.createdAt) || new Date(0);
            return dateB.getTime() - dateA.getTime();

        case 'date-asc':
            const dateA2 = a.createdAt?.toDate?.() || new Date(a.createdAt) || new Date(0);
            const dateB2 = b.createdAt?.toDate?.() || new Date(b.createdAt) || new Date(0);
            return dateA2.getTime() - dateB2.getTime();

        case 'grade-asc':
            const gradeA = parseInt((a.gradeLevel || '').replace('Grade ', '')) || 0;
            const gradeB = parseInt((b.gradeLevel || '').replace('Grade ', '')) || 0;
            return gradeA - gradeB;

        case 'grade-desc':
            const gradeA2 = parseInt((a.gradeLevel || '').replace('Grade ', '')) || 0;
            const gradeB2 = parseInt((b.gradeLevel || '').replace('Grade ', '')) || 0;
            return gradeB2 - gradeA2;

        case 'course-asc':
            const courseA = (a.course || '').toLowerCase();
            const courseB = (b.course || '').toLowerCase();
            return courseA.localeCompare(courseB);

        case 'course-desc':
            const courseA2 = (a.course || '').toLowerCase();
            const courseB2 = (b.course || '').toLowerCase();
            return courseB2.localeCompare(courseA2);

        case 'section-asc':
            const sectionA = (a.sectionName || '').toLowerCase();
            const sectionB = (b.sectionName || '').toLowerCase();
            return sectionA.localeCompare(sectionB);

        case 'section-desc':
            const sectionA2 = (a.sectionName || '').toLowerCase();
            const sectionB2 = (b.sectionName || '').toLowerCase();
            return sectionB2.localeCompare(sectionA2);

        default:
            return 0;
    }
}

// Function to load all cards from both Firestore and localStorage
async function loadCards() {
    console.log("Starting loadCards function for teacher");

    // Clear the classroom container first to avoid duplicates
    document.getElementById('teacher-classroom-container').innerHTML = '';

    try {
        console.log("Attempting to load classrooms from Firestore for teacher");

        // Make sure we have a Firestore instance
        if (!db) {
            console.log("Waiting for Firestore instance in loadCards...");
            try {
                db = await getFirestoreInstance();
                console.log("Firestore instance obtained for loadCards");
            } catch (error) {
                console.error("Failed to get Firestore instance:", error);
                throw error;
            }
        }

        // Get the teacher's email from localStorage
        const teacherData = JSON.parse(localStorage.getItem('teacherData'));
        if (!teacherData || !teacherData.email) {
            console.error("Teacher data not found in localStorage");
            throw new Error("Teacher data not found in localStorage");
        }

        const teacherEmail = teacherData.email;
        console.log("Loading classrooms for teacher:", teacherEmail);

        // Query Firestore for classrooms created by this teacher
        const classroomsCollection = collection(db, "Classrooms");
        const q = query(classroomsCollection, where("createdBy", "==", teacherEmail));
        const querySnapshot = await getDocs(q);

        console.log("Query snapshot received:", querySnapshot);
        console.log("Number of documents:", querySnapshot.size);

        // Clear existing cards from localStorage
        localStorage.removeItem('teacherCards');

        if (querySnapshot.empty) {
            console.log("No classrooms found for this teacher");
        } else {
            // Collect all classroom data first
            const classrooms = [];
            querySnapshot.forEach((doc) => {
                console.log("Processing document:", doc.id);
                const cardData = doc.data();
                cardData.id = doc.id; // Add the document ID to the card data
                console.log("Card data:", cardData);
                classrooms.push(cardData);
            });

            // Get grouping and sorting options
            const groupOption = document.getElementById('sections-group-select')?.value ||
                               localStorage.getItem('sectionsGroupPreference') || 'none';
            const sortOption = document.getElementById('sections-sort-select')?.value ||
                              localStorage.getItem('sectionsSortPreference') || 'name-asc';

            // Sort classrooms first
            classrooms.sort((a, b) => sortClassrooms(a, b, sortOption));

            // Group classrooms
            const groupedClassrooms = groupClassrooms(classrooms, groupOption);

            // Create cards for grouped classrooms
            Object.keys(groupedClassrooms).forEach(groupName => {
                const classroomsInGroup = groupedClassrooms[groupName];

                // Add group header if grouping is enabled
                if (groupOption !== 'none') {
                    const groupHeader = document.createElement('div');
                    groupHeader.className = 'col-12 mb-3';
                    groupHeader.innerHTML = `
                        <div class="group-header">
                            <div class="group-info">
                                ${groupName}
                                <span class="group-count">${classroomsInGroup.length} classroom${classroomsInGroup.length !== 1 ? 's' : ''}</span>
                            </div>
                            <button class="group-minimize-btn" type="button">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    `;
                    document.getElementById('teacher-classroom-container').appendChild(groupHeader);

                    // Create group content container
                    const groupContent = document.createElement('div');
                    groupContent.className = 'group-content row';
                    groupContent.setAttribute('data-group', groupName);
                    document.getElementById('teacher-classroom-container').appendChild(groupContent);
                }

                // Create cards for classrooms in this group
                classroomsInGroup.forEach(cardData => {
                    // Save to localStorage
                    saveCardToLocalStorage(cardData);

                    // Create card UI
                    if (groupOption !== 'none') {
                        // Append to group content container
                        const groupContent = document.querySelector(`[data-group="${groupName}"]`);
                        const cardElement = createCardElement(cardData);
                        groupContent.appendChild(cardElement);
                    } else {
                        createCard(cardData);
                    }
                });
            });

            console.log("All classrooms loaded successfully for teacher");

            // Add event listeners to minimize buttons and restore states
            setTimeout(() => {
                // Add event listeners to minimize buttons
                document.querySelectorAll('.group-minimize-btn').forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        const header = button.closest('.group-header');
                        if (!header) return;

                        // Find the group content div (next sibling of the header's parent)
                        const headerContainer = header.parentElement;
                        const content = headerContainer.nextElementSibling;

                        if (content && content.classList.contains('group-content')) {
                            const isCollapsed = content.classList.contains('collapsed');

                            if (isCollapsed) {
                                content.classList.remove('collapsed');
                                header.classList.remove('collapsed');
                                button.innerHTML = '<i class="fas fa-chevron-down"></i>';
                            } else {
                                content.classList.add('collapsed');
                                header.classList.add('collapsed');
                                button.innerHTML = '<i class="fas fa-chevron-right"></i>';
                            }

                            // Save state to localStorage
                            const groupName = header.querySelector('.group-info').textContent.trim().split('\n')[0].trim();
                            const storageKey = `groupCollapsed_materials_${groupName}`;
                            localStorage.setItem(storageKey, !isCollapsed);
                        }
                    });
                });

                // Restore group collapse states after content is loaded
                if (typeof window.restoreGroupStates === 'function') {
                    window.restoreGroupStates();
                }
            }, 100);

            // Update managed classes count in profile
            updateManagedClassesCount();
        }
    } catch (error) {
        console.error("Error loading from Firestore:", error);

        // If Firestore fails, try to load from localStorage
        try {
            console.log("Attempting to load classrooms from localStorage");
            const cards = JSON.parse(localStorage.getItem('teacherCards')) || [];
            console.log("Cards from localStorage:", cards);

            if (cards.length === 0) {
                console.log("No classrooms found in localStorage for teacher");
            } else {
                // Get grouping and sorting options
                const groupOption = document.getElementById('sections-group-select')?.value ||
                                   localStorage.getItem('sectionsGroupPreference') || 'none';
                const sortOption = document.getElementById('sections-sort-select')?.value ||
                                  localStorage.getItem('sectionsSortPreference') || 'name-asc';

                // Sort classrooms first
                cards.sort((a, b) => sortClassrooms(a, b, sortOption));

                // Group classrooms
                const groupedClassrooms = groupClassrooms(cards, groupOption);

                // Create cards for grouped classrooms
                Object.keys(groupedClassrooms).forEach(groupName => {
                    const classroomsInGroup = groupedClassrooms[groupName];

                    // Add group header if grouping is enabled
                    if (groupOption !== 'none') {
                        const groupHeader = document.createElement('div');
                        groupHeader.className = 'col-12 mb-3';
                        groupHeader.innerHTML = `
                            <div class="group-header">
                                <div class="group-info">
                                    ${groupName}
                                    <span class="group-count">${classroomsInGroup.length} classroom${classroomsInGroup.length !== 1 ? 's' : ''}</span>
                                </div>
                                <button class="group-minimize-btn" type="button">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                        `;
                        document.getElementById('teacher-classroom-container').appendChild(groupHeader);

                        // Create group content container
                        const groupContent = document.createElement('div');
                        groupContent.className = 'group-content row';
                        groupContent.setAttribute('data-group', groupName);
                        document.getElementById('teacher-classroom-container').appendChild(groupContent);
                    }

                    // Create cards for classrooms in this group
                    classroomsInGroup.forEach(cardData => {
                        if (groupOption !== 'none') {
                            // Append to group content container
                            const groupContent = document.querySelector(`[data-group="${groupName}"]`);
                            const cardElement = createCardElement(cardData);
                            groupContent.appendChild(cardElement);
                        } else {
                            createCard(cardData);
                        }
                    });
                });

                console.log("Classrooms loaded from localStorage for teacher");

                // Add event listeners to minimize buttons and restore states
                setTimeout(() => {
                    // Add event listeners to minimize buttons
                    document.querySelectorAll('.group-minimize-btn').forEach(button => {
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();

                            const header = button.closest('.group-header');
                            if (!header) return;

                            // Find the group content div (next sibling of the header's parent)
                            const headerContainer = header.parentElement;
                            const content = headerContainer.nextElementSibling;

                            if (content && content.classList.contains('group-content')) {
                                const isCollapsed = content.classList.contains('collapsed');

                                if (isCollapsed) {
                                    content.classList.remove('collapsed');
                                    header.classList.remove('collapsed');
                                    button.innerHTML = '<i class="fas fa-chevron-down"></i>';
                                } else {
                                    content.classList.add('collapsed');
                                    header.classList.add('collapsed');
                                    button.innerHTML = '<i class="fas fa-chevron-right"></i>';
                                }

                                // Save state to localStorage
                                const groupName = header.querySelector('.group-info').textContent.trim().split('\n')[0].trim();
                                const storageKey = `groupCollapsed_materials_${groupName}`;
                                localStorage.setItem(storageKey, !isCollapsed);
                            }
                        });
                    });

                    // Restore group collapse states after content is loaded
                    if (typeof window.restoreGroupStates === 'function') {
                        window.restoreGroupStates();
                    }
                }, 100);

                // Update managed classes count in profile
                updateManagedClassesCount();
            }
        } catch (localError) {
            console.error("Error loading from localStorage:", localError);
            alert("Failed to load classrooms. Please refresh the page.");
        }
    }
}

// Wait for DOM to be fully loaded before setting up event listeners
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM fully loaded for teacher classroom management");

    // Add event listener for the Manage Sections menu item
    document.getElementById('manage-sections-btn').addEventListener('click', function() {
        // Hide other containers
        document.getElementById('quizzes-and-exams-list').style.display = 'none';
        document.getElementById('students-assessments-list').style.display = 'none';
        document.getElementById('learning-materials-list').style.display = 'none';

        // Show the manage sections container
        document.getElementById('manage-sections-container').style.display = 'block';

        // Load the sections
        loadCards();
    });

    // Listen for the refresh sections event
    window.addEventListener('refreshSections', function() {
        console.log('Refresh sections event received');
        loadCards();
    });

    // Add event listeners for sections sort and group dropdowns
    const sectionsSort = document.getElementById('sections-sort-select');
    const sectionsGroup = document.getElementById('sections-group-select');

    if (sectionsSort) {
        // Load saved sort preference
        const savedSort = localStorage.getItem('sectionsSortPreference');
        if (savedSort) {
            sectionsSort.value = savedSort;
        }

        sectionsSort.addEventListener('change', function() {
            console.log('Sections sort option changed to:', this.value);
            // Save sort preference
            localStorage.setItem('sectionsSortPreference', this.value);
            loadCards();
        });
    }

    if (sectionsGroup) {
        // Load saved group preference
        const savedGroup = localStorage.getItem('sectionsGroupPreference');
        if (savedGroup) {
            sectionsGroup.value = savedGroup;
        }

        sectionsGroup.addEventListener('change', function() {
            console.log('Sections group option changed to:', this.value);
            // Save group preference
            localStorage.setItem('sectionsGroupPreference', this.value);
            loadCards();
        });
    }

    // Add event listener for the "Edit" button in the card menu
    document.addEventListener('click', function (event) {
        if (event.target.classList.contains('edit-btn')) {
            const card = event.target.closest('.card');
            editClassroom(card);
        }
    });

    // Function to show the add classroom form
    document.getElementById('teacher-add-classroom-btn').addEventListener('click', function () {
        document.getElementById('teacher-add-classroom-card').classList.remove('hidden');
    });

    // Function to cancel the add classroom form
    document.getElementById('teacher-cancel-classroom').addEventListener('click', function (e) {
        e.preventDefault();
        document.getElementById('teacher-add-classroom-card').classList.add('hidden');
    });

    // Function to handle the form submission for adding a classroom
    document.getElementById('teacher-submit-classroom').addEventListener('click', async function (event) {
        event.preventDefault(); // Prevents default form submission
        console.log("Submit classroom button clicked by teacher");

        const subjectName = document.getElementById('teacher-subject-names').value.trim();
        const sectionName = document.getElementById('teacher-section-name').value.trim();
        const details = document.getElementById('teacher-detailss').value.trim();
        const gradeLevel = document.getElementById('teacher-grade-levels').value;
        const course = document.getElementById('teacher-courses').value;
        const color = document.getElementById('teacher-color-picker').value;
        const enrollCode = generateEnrollCode();

        if (!subjectName || !sectionName || !details || !gradeLevel || !course) {
            alert('Please fill in all fields.');
            return;
        }

        // Get teacher email from localStorage
        const teacherData = JSON.parse(localStorage.getItem('teacherData'));
        if (!teacherData || !teacherData.email) {
            alert('Teacher information not found. Please log in again.');
            return;
        }

        const cardData = {
            subjectName,
            sectionName,
            details,
            gradeLevel,
            course,
            color,
            enrollCode,
            createdBy: teacherData.email,
            createdAt: new Date()
        };

        console.log("New classroom data:", cardData);

        try {
            console.log("Attempting to add classroom to Firestore");
            // Make sure we have the necessary Firebase functions
            if (!db || !collection || !addDoc) {
                throw new Error("Firebase functions not available");
            }

            // Add to Firestore
            const docRef = await addDoc(collection(db, "Classrooms"), cardData);
            console.log("Classroom added to Firestore with ID:", docRef.id);

            cardData.id = docRef.id; // Store the Firestore document ID

            // Add to localStorage
            saveCardToLocalStorage(cardData);

            // Create the card UI
            createCard(cardData);

            // Clear form
            document.getElementById('teacher-subject-names').value = '';
            document.getElementById('teacher-section-name').value = '';
            document.getElementById('teacher-detailss').value = '';
            document.getElementById('teacher-grade-levels').selectedIndex = 0;
            document.getElementById('teacher-courses').selectedIndex = 0;
            document.getElementById('teacher-add-classroom-card').classList.add('hidden');

            console.log("Classroom created successfully by teacher");
        } catch (error) {
            console.error("Error adding classroom:", error);
            alert("Failed to create classroom. Please try again.");
        }
    });

    // Close any open card menus when clicking outside
    document.addEventListener("click", function (event) {
        document.querySelectorAll(".card-menu").forEach(menu => {
            if (!menu.contains(event.target) && !event.target.classList.contains("menu-btn")) {
                menu.classList.add("hidden");
            }
        });
    });
});
