// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-app.js";
import { getFirestore, doc, getDoc, updateDoc, arrayUnion, arrayRemove, collection, query, where, getDocs, deleteDoc } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js";
import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-auth.js";

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyBuj8sMvbDKmjkAVG5JdVOdEF4OO9ijjzA",
    authDomain: "lead-login.firebaseapp.com",
    databaseURL: "https://lead-login-default-rtdb.firebaseio.com",
    projectId: "lead-login",
    storageBucket: "lead-login.appspot.com",
    messagingSenderId: "1051456252675",
    appId: "1:1051456252675:web:61073e11903055f889d736"
};

// Initialize Firebase
let app;
let db;
let auth;

try {
    console.log('Initializing Firebase with config:', firebaseConfig);
    app = initializeApp(firebaseConfig);
    db = getFirestore(app);
    auth = getAuth(app);
    console.log('Firebase initialized successfully');
    console.log('App:', app);
    console.log('Database:', db);
    console.log('Auth:', auth);
} catch (error) {
    console.error('Error initializing Firebase:', error);
    console.error('Error details:', error.message, error.stack);
}

// Get classroom ID from URL parameters
const urlParams = new URLSearchParams(window.location.search);
const classroomId = urlParams.get('id');

// Function to check if current user is admin
async function checkAdminStatus() {
    console.log('Checking admin status...');
    return new Promise((resolve) => {
        // Add timeout to prevent hanging
        const timeout = setTimeout(() => {
            console.log('Admin status check timed out');
            resolve(false);
        }, 5000);

        onAuthStateChanged(auth, async (user) => {
            clearTimeout(timeout);
            console.log('Auth state changed. User:', user ? 'Logged in' : 'Not logged in');
            if (user) {
                try {
                    // Check Users collection
                    const userDoc = await getDoc(doc(db, "Users", user.uid));
                    console.log('User document:', userDoc.exists() ? 'Found' : 'Not found');

                    if (userDoc.exists()) {
                        const userData = userDoc.data();
                        console.log('User data:', userData);

                        // Check if user is admin based on role
                        if (userData.role === "admin") {
                            console.log('User is admin based on role');
                            resolve(true);
                            return;
                        } else {
                            console.log('User is not admin. Role:', userData.role);
                        }
                    }

                    // Double check with Admins collection
                    const adminDoc = await getDoc(doc(db, "Admins", user.uid));
                    if (adminDoc.exists()) {
                        console.log('User is admin based on Admins collection');
                        resolve(true);
                        return;
                    }

                    console.log('User is not an admin');
                    resolve(false);
                } catch (error) {
                    console.error('Error checking admin status:', error);
                    resolve(false);
                }
            } else {
                console.log('No user logged in');
                resolve(false);
            }
        });
    });
}

// Function to check if current user is a teacher
async function checkTeacherStatus() {
    console.log('Checking teacher status...');
    return new Promise((resolve) => {
        // Add timeout to prevent hanging
        const timeout = setTimeout(() => {
            console.log('Teacher status check timed out');
            resolve(false);
        }, 5000);

        onAuthStateChanged(auth, async (user) => {
            clearTimeout(timeout);
            console.log('Auth state changed. User:', user ? 'Logged in' : 'Not logged in');
            if (user) {
                try {
                    // Check Users collection
                    const userDoc = await getDoc(doc(db, "Users", user.uid));
                    console.log('User document:', userDoc.exists() ? 'Found' : 'Not found');

                    if (userDoc.exists()) {
                        const userData = userDoc.data();
                        console.log('User data:', userData);

                        // Check if user is teacher based on role
                        if (userData.role === "teacher") {
                            console.log('User is teacher based on role');
                            resolve(true);
                            return;
                        } else {
                            console.log('User is not teacher. Role:', userData.role);
                        }
                    }

                    // Check Instructors collection
                    const teacherDoc = await getDoc(doc(db, "Instructors", user.uid));
                    if (teacherDoc.exists()) {
                        console.log('User is teacher based on Instructors collection');
                        resolve(true);
                        return;
                    }

                    // Check if teacher data exists in localStorage
                    const teacherData = localStorage.getItem('teacherData');
                    if (teacherData) {
                        console.log('User is teacher based on localStorage data');
                        resolve(true);
                        return;
                    }

                    console.log('User is not a teacher');
                    resolve(false);
                } catch (error) {
                    console.error('Error checking teacher status:', error);
                    resolve(false);
                }
            } else {
                console.log('No user logged in');
                resolve(false);
            }
        });
    });
}

// Function to check if current page is student page
function isStudentPage() {
    return window.location.pathname.includes('student-classroom-materials.html');
}

// Function to check if student is authenticated via localStorage
function isStudentAuthenticated() {
    const studentData = localStorage.getItem('studentData');
    if (studentData) {
        try {
            const parsed = JSON.parse(studentData);
            return parsed && parsed.email;
        } catch (error) {
            console.error('Error parsing student data:', error);
            return false;
        }
    }
    return false;
}

// Function to load classroom data and materials
async function loadClassroomData() {
    console.log('Loading classroom data...');
    console.log('Current URL:', window.location.href);
    console.log('Classroom ID from URL:', classroomId);

    if (!db) {
        console.error('Firestore not initialized');
        document.getElementById('materials-list').innerHTML =
            '<p class="text-center text-danger">Error: Database not initialized. Please refresh the page.</p>';
        return;
    }

    console.log('Database initialized successfully');

    // Check admin and teacher status for current user
    // For student pages, skip Firebase Auth checks and use localStorage
    let isAdmin = false;
    let isTeacher = false;

    if (isStudentPage()) {
        console.log('Student page detected, checking localStorage authentication');

        // Check if student is authenticated via localStorage
        if (!isStudentAuthenticated()) {
            console.error('Student not authenticated');
            document.getElementById('materials-list').innerHTML =
                '<p class="text-center text-danger">Please log in to access classroom materials.</p>';
            return;
        }

        // Students don't need admin/teacher permissions for viewing materials
        isAdmin = false;
        isTeacher = false;
    } else {
        isAdmin = await checkAdminStatus();
        isTeacher = await checkTeacherStatus();
    }

    console.log('Current user admin status:', isAdmin);
    console.log('Current user teacher status:', isTeacher);

    // Show enrolled students section for all users (students can view classmates)
    const studentsList = document.getElementById('students-list');
    if (studentsList) {
        const enrolledStudentsCard = studentsList.closest('.card');
        if (enrolledStudentsCard) {
            enrolledStudentsCard.style.display = 'block';
        }
    }

    if (!classroomId) {
        console.log('No classroom ID found in URL');
        document.getElementById('materials-list').innerHTML = '<p class="text-center text-danger">No classroom ID provided. Please access this page through the classroom list.</p>';
        return;
    }

    // Show loading indicator
    document.getElementById('materials-list').innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading...</span></div><p class="mt-2">Loading classroom materials...</p></div>';

    try {
        console.log('Attempting to fetch classroom with ID:', classroomId);
        const classroomRef = doc(db, "Classrooms", classroomId);
        const classroomDoc = await getDoc(classroomRef);
        console.log('Classroom document fetch completed. Exists:', classroomDoc.exists());

        if (!classroomDoc.exists()) {
            console.log('Classroom document not found');
            document.getElementById('materials-list').innerHTML = '<p class="text-center text-danger">Classroom not found. The classroom may have been deleted or you may not have access to it.</p>';
            return;
        }

        const classroomData = classroomDoc.data();
        console.log('Classroom data loaded successfully:', {
            subjectName: classroomData.subjectName,
            details: classroomData.details,
            materialsCount: classroomData.materials ? classroomData.materials.length : 0,
            hasColor: !!classroomData.color
        });

        // Update classroom information
        document.getElementById('classroom-title').textContent = `${classroomData.subjectName} - Materials`;
        document.getElementById('subject-name').textContent = classroomData.subjectName;
        document.getElementById('classroom-details').textContent = classroomData.details;
        document.getElementById('grade-level').textContent = classroomData.gradeLevel;

        // Only update section-name if the element exists (not present in student page)
        const sectionNameElement = document.getElementById('section-name');
        if (sectionNameElement) {
            sectionNameElement.textContent = classroomData.sectionName || 'No Section';
        }

        document.getElementById('course').textContent = classroomData.course;
        document.getElementById('enroll-code').textContent = classroomData.enrollCode;

        // Display creator information
        let creatorName = "Unknown";

        // Check if createdBy email exists
        if (classroomData.createdBy) {
            // First check if we have a direct creator name in the classroom data
            if (classroomData.creatorName) {
                creatorName = classroomData.creatorName;
            } else {
                // Try to determine if created by admin or teacher based on email
                const adminEmails = ["<EMAIL>", "<EMAIL>"]; // Add known admin emails

                if (adminEmails.includes(classroomData.createdBy)) {
                    creatorName = "Administrator";
                } else {
                    // Check if we can find the teacher's name from their email
                    creatorName = `Teacher (${classroomData.createdBy})`;

                    // Try to get a more specific name by querying the Users collection
                    // This is an async operation but we'll update the UI when it completes
                    getUserNameByEmail(classroomData.createdBy)
                        .then(name => {
                            if (name) {
                                document.getElementById('created-by').textContent = name;
                            }
                        })
                        .catch(error => {
                            console.error("Error getting user name:", error);
                        });
                }
            }
        }

        document.getElementById('created-by').textContent = creatorName;

        // Set header color if available
        if (classroomData.color) {
            document.getElementById('classroom-header').style.backgroundColor = classroomData.color;
        }

        // Display materials if they exist
        const materialsList = document.getElementById('materials-list');

        if (!classroomData.materials || classroomData.materials.length === 0) {
            console.log('No materials found in classroom');
            materialsList.innerHTML = '<p class="text-center">No materials posted yet.</p>';
        } else {
            console.log('Found materials:', classroomData.materials.length);

            // Get saved sort preference or default to timestamp
            const savedSortPreference = localStorage.getItem(`sortPreference_${classroomId}`) || 'timestamp';
            const savedSortDirection = localStorage.getItem(`sortDirection_${classroomId}`) || 'desc';

            // Sort materials based on preference
            let sortedMaterials = [...classroomData.materials];
            switch (savedSortPreference) {
                case 'title':
                    sortedMaterials.sort((a, b) => {
                        // Use natural sort order for titles
                        const collator = new Intl.Collator(undefined, {numeric: true, sensitivity: 'base'});
                        const comparison = collator.compare(a.title, b.title);
                        return savedSortDirection === 'asc' ? comparison : -comparison;
                    });
                    break;
                case 'details':
                    sortedMaterials.sort((a, b) => {
                        // Use natural sort order for details
                        const collator = new Intl.Collator(undefined, {numeric: true, sensitivity: 'base'});
                        const comparison = collator.compare(a.description || '', b.description || '');
                        return savedSortDirection === 'asc' ? comparison : -comparison;
                    });
                    break;
                default: // timestamp
                    sortedMaterials.sort((a, b) => {
                        const comparison = new Date(b.timestamp) - new Date(a.timestamp);
                        return savedSortDirection === 'asc' ? -comparison : comparison;
                    });
            }

            // Add sort controls
            let html = `
                <div class="d-flex justify-content-end mb-3">
                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" id="sortDropdown" data-toggle="dropdown" aria-expanded="false">
                            Sort by: ${savedSortPreference.charAt(0).toUpperCase() + savedSortPreference.slice(1)}
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                            <li><a class="dropdown-item sort-option ${savedSortPreference === 'timestamp' ? 'active' : ''}" href="#" data-sort="timestamp">Date Posted</a></li>
                            <li><a class="dropdown-item sort-option ${savedSortPreference === 'title' ? 'active' : ''}" href="#" data-sort="title">Title</a></li>
                            <li><a class="dropdown-item sort-option ${savedSortPreference === 'details' ? 'active' : ''}" href="#" data-sort="details">Details</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item sort-direction" href="#" data-direction="${savedSortDirection === 'asc' ? 'desc' : 'asc'}">
                                ${savedSortDirection === 'asc' ? 'Descending' : 'Ascending'}
                            </a></li>
                        </ul>
                    </div>
                </div>
                <div class="list-group" id="draggable-materials">`;

            sortedMaterials.forEach((material, index) => {
                const date = new Date(material.timestamp);
                const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();

                console.log('Rendering material:', material.title, 'Admin status:', isAdmin);

                html += `
                    <div class="list-group-item list-group-item-action draggable-material"
                         style="background-color: ${classroomData.color || '#ffffff'}"
                         draggable="true"
                         data-index="${index}">
                        <div class="d-flex w-100 justify-content-between">
                            <div class="d-flex align-items-center">
                                ${(isAdmin || isTeacher) && !isStudentPage() ? `
                                    <i class="fas fa-grip-vertical mr-3 text-muted drag-handle" style="cursor: move;"></i>
                                ` : ''}
                                <h5 class="my-1 text-black">${material.title}</h5>
                            </div>
                            <div class="d-flex align-items-center">
                                <small class="text-muted mr-2">${formattedDate}</small>
                                ${(isAdmin || (isTeacher && !isStudentPage())) ? `
                                    <button class="btn btn-sm btn-danger admin-delete-btn" data-material-index="${index}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                        <p class="mb-1 material-details">${material.description || 'No description provided.'}</p>
                        <div class="mt-2">`;

                // Add file links if available
                if (material.filePath) {
                    // Add download button
                    html += `
                        <a href="${material.filePath}" target="_blank" class="btn btn-sm btn-info mr-2" download="${material.fileName}">
                            <i class="fas fa-download"></i> Download
                        </a>`;

                    // Add view button for files - opens in modal with multiple viewing options
                    html += `
                        <button class="btn btn-sm btn-primary mr-2 view-file-btn"
                                data-file-path="${material.filePath}"
                                data-file-name="${material.fileName}"
                                data-file-size="${material.fileSize || 'Unknown'}">
                            <i class="fas fa-eye"></i> View File
                        </button>`;

                }

                // Add external link if available
                if (material.link) {
                    html += `
                        <a href="${material.link}" target="_blank" class="btn btn-sm btn-secondary">
                            <i class="fas fa-external-link-alt"></i> ${material.linkName || 'Open Link'}
                        </a>`;
                }

                html += `
                        </div>
                        <small class="text-muted">Posted by: ${material.postedBy || 'Admin'}</small>
                    </div>
                `;
            });

            html += '</div>';
            materialsList.innerHTML = html;

            // Store materials in window object for reference
            window.materials = sortedMaterials;
            console.log('Materials rendered and stored in window.materials');

            // Initialize drag and drop functionality if admin or teacher
            if ((isAdmin || isTeacher) && !isStudentPage()) {
                initializeDragAndDrop();
            }

            // Add sort event listeners
            initializeSortControls();
        }
    } catch (error) {
        console.error("Error loading classroom data:", error);
        console.error("Error details:", {
            code: error.code,
            message: error.message,
            stack: error.stack
        });

        let errorMessage = 'Error loading classroom data. ';

        if (error.code === 'permission-denied') {
            errorMessage += 'You do not have permission to access this classroom.';
        } else if (error.code === 'not-found') {
            errorMessage += 'The classroom could not be found.';
        } else if (error.code === 'unavailable') {
            errorMessage += 'The service is currently unavailable. Please try again later.';
        } else {
            errorMessage += 'Please try again later.';
        }

        document.getElementById('materials-list').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Error:</strong> ${errorMessage}
                <div class="mt-2">
                    <small><strong>Technical details:</strong> ${error.message}</small>
                </div>
                <div class="mt-3">
                    <button onclick="location.reload()" class="btn btn-primary btn-sm">
                        <i class="fas fa-redo"></i> Retry
                    </button>
                    <button onclick="window.history.back()" class="btn btn-secondary btn-sm ms-2">
                        <i class="fas fa-arrow-left"></i> Go Back
                    </button>
                </div>
            </div>
        `;
    }
}

// Function to initialize drag and drop functionality
function initializeDragAndDrop() {
    const materialsList = document.getElementById('draggable-materials');
    const materials = materialsList.getElementsByClassName('draggable-material');
    let draggedItem = null;

    // Add drag event listeners to each material
    Array.from(materials).forEach(material => {
        material.addEventListener('dragstart', handleDragStart);
        material.addEventListener('dragend', handleDragEnd);
        material.addEventListener('dragover', handleDragOver);
        material.addEventListener('dragenter', handleDragEnter);
        material.addEventListener('dragleave', handleDragLeave);
        material.addEventListener('drop', handleDrop);
    });

    function handleDragStart(e) {
        draggedItem = this;
        this.classList.add('dragging');
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', this.innerHTML);
    }

    function handleDragEnd(e) {
        this.classList.remove('dragging');
        Array.from(materials).forEach(material => {
            material.classList.remove('drag-over');
        });
    }

    function handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        return false;
    }

    function handleDragEnter(e) {
        this.classList.add('drag-over');
    }

    function handleDragLeave(e) {
        this.classList.remove('drag-over');
    }

    function handleDrop(e) {
        e.stopPropagation();
        if (draggedItem !== this) {
            const allMaterials = Array.from(materials);
            const draggedIndex = allMaterials.indexOf(draggedItem);
            const droppedIndex = allMaterials.indexOf(this);

            if (draggedIndex < droppedIndex) {
                this.parentNode.insertBefore(draggedItem, this.nextSibling);
            } else {
                this.parentNode.insertBefore(draggedItem, this);
            }

            // Update the order in Firebase
            updateMaterialsOrder();
        }
        return false;
    }
}

// Function to update materials order in Firebase
async function updateMaterialsOrder() {
    try {
        const materialsList = document.getElementById('draggable-materials');
        const materials = materialsList.getElementsByClassName('draggable-material');
        const newOrder = Array.from(materials).map(material => {
            const index = parseInt(material.getAttribute('data-index'));
            return window.materials[index];
        });

        const classroomRef = doc(db, "Classrooms", classroomId);
        await updateDoc(classroomRef, {
            materials: newOrder
        });

        // Update window.materials with new order
        window.materials = newOrder;

        // Show success message
        const toast = document.createElement('div');
        toast.className = 'position-fixed bottom-0 end-0 p-3';
        toast.style.zIndex = '5';
        toast.innerHTML = `
            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <strong class="me-auto">Success</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    Materials order updated successfully!
                </div>
            </div>
        `;
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 3000);
    } catch (error) {
        console.error("Error updating materials order:", error);
        alert('Failed to update materials order. Please try again.');
    }
}

// Add CSS for drag and drop
const style = document.createElement('style');
style.textContent = `
    .draggable-material {
        transition: background-color 0.2s ease;
    }
    .draggable-material.dragging {
        opacity: 0.5;
        background-color: #f8f9fa !important;
    }
    .draggable-material.drag-over {
        border-top: 2px solid #4e73df;
    }
    .drag-handle {
        cursor: move;
    }
    .drag-handle:hover {
        color: #4e73df !important;
    }
`;
document.head.appendChild(style);

// Function to create a new material entry
function createMaterialEntry() {
    const entry = document.createElement('div');
    entry.className = 'material-entry mb-4';
    entry.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0">New Material</h6>
            <button type="button" class="btn btn-sm btn-danger remove-material">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="form-group">
            <label>Title</label>
            <input type="text" class="form-control" name="materialTitle" required>
        </div>
        <div class="form-group">
            <label>Details</label>
            <textarea class="form-control" name="materialDetails" rows="3" required></textarea>
        </div>
        <div class="form-group">
            <label>Upload File</label>
            <input type="file" class="form-control" name="materialFile">
            <small class="form-text text-muted">Upload PDF, Word, PowerPoint, or other document files (max 1GB)</small>
        </div>
        <div class="form-group">
            <label>Link Name (Optional)</label>
            <input type="text" class="form-control" name="materialLinkName" placeholder="e.g., Download PDF, Watch Video">
        </div>
        <div class="form-group">
            <label>External Link URL (Optional)</label>
            <input type="url" class="form-control" name="materialLink" placeholder="https://">
            <small class="form-text text-muted">You can provide a file upload, an external link, or both</small>
        </div>
    `;
    return entry;
}

// Function to upload a file and return its information
async function uploadFile(file) {
    return new Promise((resolve, reject) => {
        if (!file) {
            resolve(null); // No file to upload
            return;
        }

        // Check file size before upload (1GB limit)
        const maxSize = 1024 * 1024 * 1024; // 1GB in bytes
        if (file.size > maxSize) {
            reject(new Error('File size exceeds the maximum limit of 1GB'));
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        // Create XMLHttpRequest for progress tracking
        const xhr = new XMLHttpRequest();

        // Set up progress tracking for large files
        if (file.size > 10 * 1024 * 1024) { // Show progress for files > 10MB
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    console.log(`Upload progress: ${percentComplete.toFixed(1)}%`);
                    // You can add a progress bar here if needed
                }
            });
        }

        xhr.onload = function() {
            if (xhr.status === 200) {
                try {
                    const data = JSON.parse(xhr.responseText);
                    if (data.success) {
                        resolve(data.file);
                    } else {
                        reject(new Error(data.message || 'File upload failed'));
                    }
                } catch (e) {
                    reject(new Error('Invalid response from server'));
                }
            } else {
                reject(new Error(`Upload failed with status: ${xhr.status}`));
            }
        };

        xhr.onerror = function() {
            console.error('Network error during file upload');
            reject(new Error('Network error during file upload. Please check your internet connection and try again.'));
        };

        xhr.ontimeout = function() {
            console.error('Upload timeout for file:', file.name, 'Size:', file.size);
            reject(new Error('Upload timed out. This may happen with very large files or slow connections. Try:\n• Using a smaller file\n• Checking your internet connection\n• Trying again later when the server is less busy'));
        };

        // Set timeout for large files (30 minutes)
        xhr.timeout = 30 * 60 * 1000; // 30 minutes

        xhr.open('POST', 'upload_file.php');
        xhr.send(formData);
    });
}

// Function to post materials to the classroom
async function postMaterial() {
    const materialsContainer = document.getElementById('materials-container');
    const materialEntries = materialsContainer.getElementsByClassName('material-entry');
    const materials = [];

    // Validate at least one material has a title
    let hasValidMaterial = false;
    for (const entry of materialEntries) {
        const title = entry.querySelector('[name="materialTitle"]').value;
        if (title) {
            hasValidMaterial = true;
            break;
        }
    }

    if (!hasValidMaterial) {
        alert('Please enter at least one material title');
        return;
    }

    try {
        // Show loading indicator
        const submitBtn = document.getElementById('submitMaterial');
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
        submitBtn.disabled = true;

        // Process each material entry
        for (const entry of materialEntries) {
            const title = entry.querySelector('[name="materialTitle"]').value;
            if (!title) continue; // Skip empty entries

            const description = entry.querySelector('[name="materialDetails"]').value;
            const linkName = entry.querySelector('[name="materialLinkName"]').value;
            const link = entry.querySelector('[name="materialLink"]').value;
            const fileInput = entry.querySelector('[name="materialFile"]');

            // Create the material object
            const material = {
                title: title,
                description: description,
                timestamp: new Date().toISOString()
            };

            // Check if user is admin or teacher and set the appropriate poster information
            const isAdmin = await checkAdminStatus();
            const isTeacher = await checkTeacherStatus();

            if (isAdmin) {
                material.postedBy = 'Admin';
                // Try to get admin email if available
                const adminData = localStorage.getItem('adminData');
                if (adminData) {
                    try {
                        const parsedAdminData = JSON.parse(adminData);
                        if (parsedAdminData.email) {
                            material.postedByEmail = parsedAdminData.email;
                        }
                    } catch (e) {
                        console.error('Error parsing admin data:', e);
                    }
                }
            } else if (isTeacher) {
                const teacherData = localStorage.getItem('teacherData');
                if (teacherData) {
                    try {
                        const parsedTeacherData = JSON.parse(teacherData);
                        material.postedBy = `${parsedTeacherData.firstName} ${parsedTeacherData.lastName}`;
                        material.postedByEmail = parsedTeacherData.email;
                    } catch (e) {
                        console.error('Error parsing teacher data:', e);
                        material.postedBy = 'Teacher';
                    }
                } else {
                    material.postedBy = 'Teacher';
                }
            } else {
                material.postedBy = 'User';
            }

            // Handle file upload if a file is selected
            if (fileInput && fileInput.files.length > 0) {
                try {
                    const fileInfo = await uploadFile(fileInput.files[0]);
                    if (fileInfo) {
                        material.fileName = fileInfo.original_name;
                        material.filePath = fileInfo.url;
                        material.fileType = fileInfo.type;
                        material.fileSize = fileInfo.size;

                        // If no link name is provided, use the file name as the link name
                        if (!linkName) {
                            material.linkName = fileInfo.original_name;
                        } else {
                            material.linkName = linkName;
                        }
                    }
                } catch (uploadError) {
                    console.error("Error uploading file:", uploadError);
                    alert(`Error uploading file: ${uploadError.message}`);

                    // Reset button state
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                    return;
                }
            }

            // Add external link if provided
            if (link) {
                material.link = link;
                if (!material.linkName) {
                    material.linkName = linkName || 'Open Link';
                }
            }

            materials.push(material);
        }

        // Update the classroom document with all new materials
        const classroomRef = doc(db, "Classrooms", classroomId);
        await updateDoc(classroomRef, {
            materials: arrayUnion(...materials)
        });

        // Reset button state
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;

        // Close the modal
        $('#postMaterialModal').modal('hide');

        // Reset the form
        document.getElementById('postMaterialForm').reset();
        materialsContainer.innerHTML = '';
        materialsContainer.appendChild(createMaterialEntry());

        // Reload the classroom data to show the new materials
        await loadClassroomData();

        // Show success message
        alert(`Successfully posted ${materials.length} material(s)!`);
    } catch (error) {
        console.error("Error posting materials:", error);
        alert('Failed to post materials. Please try again.');

        // Reset button state
        const submitBtn = document.getElementById('submitMaterial');
        submitBtn.innerHTML = 'Post Materials';
        submitBtn.disabled = false;
    }
}

// Function to delete material
async function deleteMaterial(index) {
    // Check if on student page
    if (isStudentPage()) {
        alert('This action is not available on the student page.');
        return;
    }

    // Check if user is admin or teacher before allowing deletion
    const isAdmin = await checkAdminStatus();
    const isTeacher = await checkTeacherStatus();

    if (!isAdmin && !isTeacher) {
        alert('Only administrators and teachers can delete materials.');
        return;
    }

    const material = window.materials[index];
    if (!material) {
        alert('Material not found.');
        return;
    }

    // If user is a teacher, check if they are the one who posted the material
    if (isTeacher && !isAdmin) {
        const teacherData = JSON.parse(localStorage.getItem('teacherData'));
        if (teacherData && material.postedByEmail && material.postedByEmail !== teacherData.email) {
            alert('You can only delete materials that you have posted.');
            return;
        }
    }

    if (!confirm('Are you sure you want to delete this material? This action cannot be undone.')) {
        return;
    }

    try {
        const classroomRef = doc(db, "Classrooms", classroomId);
        await updateDoc(classroomRef, {
            materials: arrayRemove(material)
        });

        // Reload the classroom data to update the materials list
        await loadClassroomData();
        alert('Material deleted successfully!');
    } catch (error) {
        console.error("Error deleting material:", error);
        alert('Failed to delete material. Please try again.');
    }
}

// Make deleteMaterial function available globally
window.deleteMaterial = deleteMaterial;

// Function to load enrolled students for the classroom
async function loadEnrolledStudents() {
    try {
        console.log('Loading enrolled students for classroom:', classroomId);

        // Check if user is admin or teacher (for remove functionality)
        // For student pages, skip Firebase Auth checks
        let isAdmin = false;
        let isTeacher = false;
        const isStudentPageView = isStudentPage();

        if (!isStudentPageView) {
            isAdmin = await checkAdminStatus();
            isTeacher = await checkTeacherStatus();
        }

        const canRemoveStudents = (isAdmin || isTeacher) && !isStudentPageView;

        // Query Firestore for enrollments in this classroom
        const enrollmentsRef = collection(db, "Enrollments");
        const q = query(enrollmentsRef, where("classroomId", "==", classroomId));
        const querySnapshot = await getDocs(q);

        if (querySnapshot.empty) {
            console.log('No students enrolled in this classroom');
            document.getElementById('students-list').innerHTML =
                '<p class="text-center">No students enrolled in this classroom yet.</p>';
            return;
        }

        // Create a table to display enrolled students
        let html = `
            <div class="table-responsive">
                <table class="table table-bordered" id="enrolledStudentsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Enrolled On</th>
                            ${canRemoveStudents ? '<th>Actions</th>' : ''}
                        </tr>
                    </thead>
                    <tbody>
        `;

        // Add each enrolled student to the table
        querySnapshot.forEach((doc) => {
            const enrollment = doc.data();
            const enrollmentId = doc.id;

            // Format the enrollment date
            const enrolledDate = enrollment.enrolledAt ? new Date(enrollment.enrolledAt) : new Date();
            const formattedDate = enrolledDate.toLocaleDateString() + ' ' + enrolledDate.toLocaleTimeString();

            html += `
                <tr>
                    <td>${enrollment.studentName || 'Unknown'}</td>
                    <td>${enrollment.studentEmail || enrollment.studentId || 'Unknown'}</td>
                    <td>${formattedDate}</td>
                    ${canRemoveStudents ? `
                    <td>
                        <button class="btn btn-danger btn-sm remove-student-btn"
                                data-enrollment-id="${enrollmentId}"
                                data-student-name="${enrollment.studentName || 'this student'}"
                                data-student-email="${enrollment.studentEmail || enrollment.studentId}">
                            <i class="fas fa-user-minus"></i> Remove
                        </button>
                    </td>
                    ` : ''}
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        // Display the table
        document.getElementById('students-list').innerHTML = html;

        // Add event listeners to remove buttons (only if user can remove students)
        if (canRemoveStudents) {
            document.querySelectorAll('.remove-student-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const enrollmentId = this.getAttribute('data-enrollment-id');
                    const studentName = this.getAttribute('data-student-name');
                    const studentEmail = this.getAttribute('data-student-email');

                    // Set values in the confirmation modal
                    document.getElementById('enrollment-id-to-remove').value = enrollmentId;
                    document.getElementById('student-name-to-remove').textContent = studentName;
                    document.getElementById('student-email-to-remove').value = studentEmail;

                    // Show the confirmation modal
                    $('#removeStudentModal').modal('show');
                });
            });
        }

    } catch (error) {
        console.error('Error loading enrolled students:', error);
        document.getElementById('students-list').innerHTML =
            `<p class="text-center text-danger">Error loading enrolled students: ${error.message}</p>`;
    }
}

// Function to remove a student from the classroom
async function removeStudent(enrollmentId) {
    try {
        console.log('Removing student with enrollment ID:', enrollmentId);

        // Check if user is admin or teacher
        const isAdmin = await checkAdminStatus();
        const isTeacher = await checkTeacherStatus();

        if (!isAdmin && !isTeacher) {
            console.log('User is not authorized to remove students');
            alert('You do not have permission to remove students from this classroom.');
            return false;
        }

        // Delete the enrollment document from Firestore
        const enrollmentRef = doc(db, "Enrollments", enrollmentId);
        await deleteDoc(enrollmentRef);

        console.log('Student removed successfully');
        return true;
    } catch (error) {
        console.error('Error removing student:', error);
        alert(`Error removing student: ${error.message}`);
        return false;
    }
}

// Function to get user name by email
async function getUserNameByEmail(email) {
    if (!email || !db) return null;

    try {
        // First check Users collection
        const usersRef = collection(db, "Users");
        const q = query(usersRef, where("email", "==", email));
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
            const userData = querySnapshot.docs[0].data();
            if (userData.firstName && userData.lastName) {
                return `${userData.firstName} ${userData.lastName}`;
            } else if (userData.displayName) {
                return userData.displayName;
            }
        }

        // Check Instructors collection
        const instructorsRef = collection(db, "Instructors");
        const instructorQuery = query(instructorsRef, where("email", "==", email));
        const instructorSnapshot = await getDocs(instructorQuery);

        if (!instructorSnapshot.empty) {
            const instructorData = instructorSnapshot.docs[0].data();
            if (instructorData.firstName && instructorData.lastName) {
                return `${instructorData.firstName} ${instructorData.lastName}`;
            } else if (instructorData.displayName) {
                return instructorData.displayName;
            }
        }

        // Check Admins collection
        const adminsRef = collection(db, "Admins");
        const adminQuery = query(adminsRef, where("email", "==", email));
        const adminSnapshot = await getDocs(adminQuery);

        if (!adminSnapshot.empty) {
            const adminData = adminSnapshot.docs[0].data();
            if (adminData.firstName && adminData.lastName) {
                return `${adminData.firstName} ${adminData.lastName}`;
            } else if (adminData.displayName) {
                return adminData.displayName;
            } else {
                return "Administrator";
            }
        }

        return null;
    } catch (error) {
        console.error("Error getting user name by email:", error);
        return null;
    }
}

// Function to initialize sort controls
function initializeSortControls() {
    // Handle sort option clicks
    document.querySelectorAll('.sort-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const sortBy = this.getAttribute('data-sort');
            const currentDirection = localStorage.getItem(`sortDirection_${classroomId}`) || 'desc';

            // Save sort preference
            localStorage.setItem(`sortPreference_${classroomId}`, sortBy);

            // Reload materials with new sort
            loadClassroomData();
        });
    });

    // Handle sort direction clicks
    document.querySelector('.sort-direction').addEventListener('click', function(e) {
        e.preventDefault();
        const newDirection = this.getAttribute('data-direction');

        // Save sort direction
        localStorage.setItem(`sortDirection_${classroomId}`, newDirection);

        // Reload materials with new sort direction
        loadClassroomData();
    });
}

// Function to handle the back button click
function handleBackButtonClick() {
    // Mark as internal navigation to prevent logout
    if (window.isNavigatingWithinLMS !== undefined) {
        window.isNavigatingWithinLMS = true;
    }

    // Check if we're on the student page
    if (isStudentPage()) {
        // For student page, always go back to Student11.html
        window.location.href = 'Student11.html';
        return;
    }

    // Get the referrer (previous page)
    const referrer = document.referrer;

    // Check if we came from Teacher.html or Admin.html
    if (referrer.includes('Teacher.html')) {
        window.location.href = 'Teacher.html';
    } else if (referrer.includes('Admin.html')) {
        window.location.href = 'Admin.html';
    } else if (referrer.includes('Student11.html')) {
        window.location.href = 'Student11.html';
    } else if (referrer.includes('Student12.html')) {
        window.location.href = 'Student12.html';
    } else {
        // Default fallback - check localStorage for user type
        const teacherData = localStorage.getItem('teacherData');
        const adminData = localStorage.getItem('adminData');
        const studentData = localStorage.getItem('studentData');

        if (teacherData) {
            window.location.href = 'Teacher.html';
        } else if (adminData) {
            window.location.href = 'Admin.html';
        } else if (studentData) {
            // Always go back to Student11.html for student users
            window.location.href = 'Student11.html';
        } else {
            // If all else fails, go to index.html as default
            window.location.href = 'index.html';
        }
    }
}

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', async function() {
    console.log('DOM loaded, initializing page...');
    console.log('Current URL:', window.location.href);
    console.log('URL search params:', window.location.search);
    console.log('Classroom ID extracted:', classroomId);

    // Show debug info temporarily
    if (!classroomId) {
        document.getElementById('materials-list').innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <h5>No Classroom ID Found</h5>
                <p>The classroom ID is missing from the URL.</p>
                <p><strong>Current URL:</strong> ${window.location.href}</p>
                <p><strong>Expected format:</strong> classroom-materials.html?id=YOUR_CLASSROOM_ID</p>
                <div class="mt-3">
                    <button onclick="window.history.back()" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Go Back to Classrooms
                    </button>
                </div>
            </div>
        `;
        return;
    }

    // Check Firebase status
    if (!db) {
        document.getElementById('materials-list').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <h5>Database Connection Error</h5>
                <p>Unable to connect to the database. Please try refreshing the page.</p>
                <div class="mt-3">
                    <button onclick="location.reload()" class="btn btn-primary">
                        <i class="fas fa-redo"></i> Refresh Page
                    </button>
                </div>
            </div>
        `;
        return;
    }

    console.log('Firebase status OK, loading classroom data...');

    // Load initial classroom data
    await loadClassroomData();

    // Load enrolled students
    await loadEnrolledStudents();

    // Get the post material button
    const postMaterialBtn = document.getElementById('post-material-btn');

    // Get the refresh students button
    const refreshStudentsBtn = document.getElementById('refresh-students-btn');
    if (refreshStudentsBtn) {
        refreshStudentsBtn.addEventListener('click', async function() {
            // Show loading message
            document.getElementById('students-list').innerHTML = '<p class="text-center">Loading enrolled students...</p>';
            // Reload enrolled students
            await loadEnrolledStudents();
        });
    }

    // Handle confirm remove student button
    const confirmRemoveStudentBtn = document.getElementById('confirm-remove-student');
    if (confirmRemoveStudentBtn) {
        confirmRemoveStudentBtn.addEventListener('click', async function() {
            const enrollmentId = document.getElementById('enrollment-id-to-remove').value;

            // Disable the button and show loading state
            confirmRemoveStudentBtn.disabled = true;
            confirmRemoveStudentBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Removing...';

            // Remove the student
            const success = await removeStudent(enrollmentId);

            if (success) {
                // Hide the modal
                $('#removeStudentModal').modal('hide');

                // Reload the enrolled students list
                await loadEnrolledStudents();

                // Show success message
                alert('Student removed successfully from the classroom.');
            }

            // Reset the button
            confirmRemoveStudentBtn.disabled = false;
            confirmRemoveStudentBtn.innerHTML = 'Remove Student';
        });
    }

    // Show modal when post material button is clicked
    postMaterialBtn.addEventListener('click', async function() {
        // Check if on student page
        if (isStudentPage()) {
            alert('This action is not available on the student page.');
            return;
        }

        // Check if user is admin or teacher before showing the modal
        const isAdmin = await checkAdminStatus();
        const isTeacher = await checkTeacherStatus();

        if (!isAdmin && !isTeacher) {
            alert('Only administrators and teachers can post materials.');
            return;
        }
        $('#postMaterialModal').modal('show');
    });

    // Add event listener for back button
    const backButton = document.getElementById('back-to-classrooms');
    if (backButton) {
        // For student page, use direct link in HTML
        if (isStudentPage()) {
            // No event listener needed, direct href will work
            console.log('Using direct link for student page');
        } else {
            // For admin/teacher pages, use the event handler
            backButton.addEventListener('click', function(e) {
                e.preventDefault();
                handleBackButtonClick();
            });
        }
    }

    // Handle form submission
    document.getElementById('submitMaterial').addEventListener('click', postMaterial);

    // Handle adding another material
    document.getElementById('addAnotherMaterial').addEventListener('click', function() {
        const materialsContainer = document.getElementById('materials-container');
        materialsContainer.appendChild(createMaterialEntry());
    });

    // Handle removing material entries
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-material')) {
            const entry = e.target.closest('.material-entry');
            if (document.getElementsByClassName('material-entry').length > 1) {
                entry.remove();
            } else {
                alert('You must have at least one material entry.');
            }
        }
    });

    // Clear form when modal is closed
    $('#postMaterialModal').on('hidden.bs.modal', function() {
        document.getElementById('postMaterialForm').reset();
        const materialsContainer = document.getElementById('materials-container');
        materialsContainer.innerHTML = '';
        materialsContainer.appendChild(createMaterialEntry());
    });

    // Add event listeners for delete buttons
    document.addEventListener('click', async function(e) {
        if (e.target.closest('.admin-delete-btn')) {
            const button = e.target.closest('.admin-delete-btn');
            const index = button.getAttribute('data-material-index');
            await deleteMaterial(index);
        }

        // View file button click handler
        if (e.target.closest('.view-file-btn')) {
            const button = e.target.closest('.view-file-btn');
            const filePath = button.getAttribute('data-file-path');
            const fileName = button.getAttribute('data-file-name');
            const fileSize = button.getAttribute('data-file-size');

            openFilePreview(filePath, fileName, fileSize);
        }
    });

    console.log('Page initialization completed');
});

// Function to open file preview modal
function openFilePreview(filePath, fileName, fileSize) {
    // Set modal title
    document.getElementById('filePreviewModalLabel').textContent = `Preview: ${fileName}`;

    // Set download button
    const downloadBtn = document.getElementById('downloadFileBtn');
    downloadBtn.href = filePath;
    downloadBtn.download = fileName;

    // Show loading state
    const previewContent = document.getElementById('filePreviewContent');
    previewContent.innerHTML = `
        <div class="file-preview-loading text-center">
            <div class="spinner-border text-primary" role="status"></div>
            <p>Loading file preview...</p>
        </div>
    `;

    // Show the modal
    $('#filePreviewModal').modal('show');

    // Load file preview content
    loadFilePreview(filePath, fileName, fileSize);
}

// Function to load file preview content
function loadFilePreview(filePath, fileName, fileSize) {
    const previewContent = document.getElementById('filePreviewContent');
    const fileExtension = fileName.split('.').pop().toLowerCase();

    // Enhanced URL validation and error handling
    const getAbsoluteUrl = (relativeUrl) => {
        try {
            const a = document.createElement('a');
            a.href = relativeUrl;
            return a.href;
        } catch (error) {
            console.error('Error creating absolute URL:', error);
            return relativeUrl;
        }
    };

    // Test file accessibility before attempting to load
    const testFileAccess = async (url) => {
        try {
            const response = await fetch(url, { method: 'HEAD' });
            return {
                accessible: response.ok,
                status: response.status,
                statusText: response.statusText
            };
        } catch (error) {
            console.error('File access test failed:', error);
            return {
                accessible: false,
                error: error.message
            };
        }
    };

    const absoluteFileUrl = getAbsoluteUrl(filePath);
    const encodedFileUrl = encodeURIComponent(absoluteFileUrl);

    // Create file info section
    const fileInfo = `
        <div class="file-preview-info mb-3 p-3 bg-light border rounded">
            <div class="row">
                <div class="col-md-8">
                    <h6 class="mb-1"><i class="fas fa-file"></i> ${fileName}</h6>
                    <small class="text-muted">Size: ${fileSize}</small>
                </div>
                <div class="col-md-4 text-right">
                    <span class="badge badge-primary">${fileExtension.toUpperCase()}</span>
                </div>
            </div>
        </div>
    `;

    let previewHtml = '';

    // Handle different file types
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(fileExtension)) {
        // Image files - check file size for better handling
        const fileSizeMB = fileSize ? (fileSize / (1024 * 1024)).toFixed(2) : 'Unknown';
        const isVeryLargeImage = fileSize && fileSize > (500 * 1024 * 1024); // 500MB threshold
        const isLargeImage = fileSize && fileSize > (100 * 1024 * 1024); // 100MB threshold

        if (isVeryLargeImage) {
            // Very large image (500MB+) - recommend download or new tab
            previewHtml = `
                <div class="file-preview-options text-center">
                    <h5><i class="fas fa-image text-danger"></i> Very Large Image File</h5>
                    <p class="text-muted mb-3">This image is ${fileSizeMB} MB and may cause browser issues.</p>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Performance Warning:</strong> This image is very large (${fileSizeMB} MB) and may cause browser crashes or extreme slowness.
                    </div>
                    <div class="mt-3">
                        <a href="${absoluteFileUrl}" target="_blank" class="btn btn-primary btn-lg">
                            <i class="fas fa-external-link-alt"></i> Open in New Tab (Recommended)
                        </a>
                        <a href="${filePath}" download="${fileName}" class="btn btn-success btn-lg ms-2">
                            <i class="fas fa-download"></i> Download Image (Safest)
                        </a>
                        <button onclick="loadLargeImage('${filePath}', '${fileName}')" class="btn btn-danger btn-lg ms-2">
                            <i class="fas fa-exclamation-triangle"></i> Force Load (Risk)
                        </button>
                    </div>
                </div>
            `;
        } else if (isLargeImage) {
            // Large image (100-500MB) - provide options with warning
            previewHtml = `
                <div class="file-preview-options text-center">
                    <h5><i class="fas fa-image text-warning"></i> Large Image File</h5>
                    <p class="text-muted mb-3">This image is ${fileSizeMB} MB and may take time to load.</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle"></i>
                        <strong>Large File Notice:</strong> This image may load slowly or cause browser performance issues.
                    </div>
                    <div class="mt-3">
                        <a href="${absoluteFileUrl}" target="_blank" class="btn btn-primary btn-lg">
                            <i class="fas fa-external-link-alt"></i> Open Image in New Tab
                        </a>
                        <button onclick="loadLargeImage('${filePath}', '${fileName}')" class="btn btn-warning btn-lg ms-2">
                            <i class="fas fa-eye"></i> Load Preview (May be slow)
                        </button>
                        <a href="${filePath}" download="${fileName}" class="btn btn-success btn-lg ms-2">
                            <i class="fas fa-download"></i> Download Image
                        </a>
                    </div>
                </div>
            `;
        } else {
            // Regular image - direct preview with enhanced error handling
            previewHtml = `
                <div class="text-center">
                    <div class="image-loading" id="imageLoading" style="display: block;">
                        <div class="spinner-border text-primary" role="status"></div>
                        <p class="mt-2">Loading image...</p>
                    </div>
                    <img id="previewImage" class="img-fluid" style="max-height: 60vh; display: none;" alt="${fileName}"
                         onload="handleImageLoad(this)"
                         onerror="handleImageError('${filePath}', '${fileName}')">
                    <div id="imageError" style="display: none;" class="file-preview-error">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h6>Failed to load image</h6>
                            <p>The image could not be displayed. This may be due to:</p>
                            <ul class="text-left" style="display: inline-block;">
                                <li>File path issues</li>
                                <li>Network connectivity problems</li>
                                <li>File corruption or invalid format</li>
                                <li>Server access restrictions</li>
                                <li>Browser compatibility issues</li>
                            </ul>
                            <div class="mt-2">
                                <small class="text-muted">File path: ${filePath}</small>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="${absoluteFileUrl}" target="_blank" class="btn btn-primary">
                                <i class="fas fa-external-link-alt"></i> Open in New Tab
                            </a>
                            <a href="${filePath}" download="${fileName}" class="btn btn-success ms-2">
                                <i class="fas fa-download"></i> Download
                            </a>
                            <button onclick="retryImageLoad('${filePath}', '${fileName}')" class="btn btn-warning ms-2">
                                <i class="fas fa-redo"></i> Retry
                            </button>
                            <button onclick="testImagePath('${filePath}', '${fileName}')" class="btn btn-info ms-2">
                                <i class="fas fa-tools"></i> Test Path
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
    } else if (['pdf'].includes(fileExtension)) {
        // PDF files - Enhanced handling for large files
        const fileSizeMB = fileSize ? (fileSize / (1024 * 1024)).toFixed(2) : 'Unknown';
        const isLargePDF = fileSize && fileSize > (25 * 1024 * 1024); // 25MB threshold for Google Docs Viewer
        const isVeryLargePDF = fileSize && fileSize > (100 * 1024 * 1024); // 100MB threshold

        let sizeWarning = '';
        let viewerOptions = '';

        if (isVeryLargePDF) {
            sizeWarning = `
                <div class="alert alert-warning mb-3">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Large PDF File (${fileSizeMB} MB)</strong><br>
                    For best performance, we recommend downloading this file or opening it directly in your browser.
                </div>
            `;
            viewerOptions = `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="${absoluteFileUrl}" target="_blank" class="btn btn-primary btn-lg btn-block">
                            <i class="fas fa-external-link-alt"></i><br>
                            <small>Open in Browser</small>
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="${absoluteFileUrl}" download="${fileName}" class="btn btn-success btn-lg btn-block">
                            <i class="fas fa-download"></i><br>
                            <small>Download PDF</small>
                        </a>
                    </div>
                </div>
            `;
        } else if (isLargePDF) {
            sizeWarning = `
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>Medium PDF File (${fileSizeMB} MB)</strong><br>
                    Google Docs Viewer may have issues with this file size. Try opening directly in browser if preview fails.
                </div>
            `;
            viewerOptions = `
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="${absoluteFileUrl}" target="_blank" class="btn btn-primary btn-lg btn-block">
                            <i class="fas fa-external-link-alt"></i><br>
                            <small>Open in Browser</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="https://docs.google.com/viewer?url=${encodedFileUrl}&embedded=true" target="_blank" class="btn btn-info btn-lg btn-block">
                            <i class="fas fa-eye"></i><br>
                            <small>Google Viewer</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="${absoluteFileUrl}" download="${fileName}" class="btn btn-success btn-lg btn-block">
                            <i class="fas fa-download"></i><br>
                            <small>Download</small>
                        </a>
                    </div>
                </div>
            `;
        } else {
            viewerOptions = `
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="${absoluteFileUrl}" target="_blank" class="btn btn-primary btn-lg btn-block">
                            <i class="fas fa-external-link-alt"></i><br>
                            <small>Open in Browser</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="https://docs.google.com/viewer?url=${encodedFileUrl}&embedded=true" target="_blank" class="btn btn-info btn-lg btn-block">
                            <i class="fas fa-eye"></i><br>
                            <small>Google Viewer</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="${absoluteFileUrl}" download="${fileName}" class="btn btn-success btn-lg btn-block">
                            <i class="fas fa-download"></i><br>
                            <small>Download</small>
                        </a>
                    </div>
                </div>
            `;
        }

        previewHtml = `
            <div class="file-preview-options text-center">
                <h5><i class="fas fa-file-pdf text-danger"></i> PDF Document</h5>
                <p class="text-muted mb-3">File size: ${fileSizeMB} MB</p>

                ${sizeWarning}

                <p class="text-muted mb-4">Choose how you'd like to view this PDF file:</p>

                ${viewerOptions}

                ${!isVeryLargePDF ? `
                <div class="mt-4">
                    <h6>Preview</h6>
                    <iframe src="${filePath}" width="100%" height="500px" style="border: 1px solid #ddd; border-radius: 4px;"
                            onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    </iframe>
                    <div style="display: none;" class="file-preview-error mt-3">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Direct preview not available</strong><br>
                            This PDF cannot be previewed directly. Please use one of the viewing options above.
                        </div>
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension)) {
        // Microsoft Office files
        previewHtml = `
            <div class="file-preview-options text-center">
                <h5><i class="fas fa-file-word text-primary"></i> Office Document</h5>
                <p class="text-muted mb-4">Choose how you'd like to view this document:</p>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="${absoluteFileUrl}" target="_blank" class="btn btn-primary btn-lg btn-block">
                            <i class="fas fa-external-link-alt"></i><br>
                            <small>Open Directly</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="https://docs.google.com/viewer?url=${encodedFileUrl}&embedded=true" target="_blank" class="btn btn-info btn-lg btn-block">
                            <i class="fas fa-eye"></i><br>
                            <small>Google Docs Viewer</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="https://view.officeapps.live.com/op/embed.aspx?src=${encodedFileUrl}" target="_blank" class="btn btn-success btn-lg btn-block">
                            <i class="fas fa-file-alt"></i><br>
                            <small>Office Online</small>
                        </a>
                    </div>
                </div>
            </div>
        `;
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v', '3gp', 'ogv'].includes(fileExtension)) {
        // Video files
        previewHtml = `
            <div class="file-preview-options text-center">
                <h5><i class="fas fa-video text-primary"></i> Video File</h5>
                <p class="text-muted mb-4">Video player with download option:</p>

                <div class="video-preview-container mb-4">
                    <video controls class="w-100" style="max-height: 60vh; border-radius: 8px; background: #000;">
                        <source src="${filePath}" type="video/${fileExtension}">
                        Your browser does not support the video tag.
                    </video>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="${absoluteFileUrl}" target="_blank" class="btn btn-primary btn-lg btn-block">
                            <i class="fas fa-external-link-alt"></i><br>
                            <small>Open in New Tab</small>
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="${filePath}" download="${fileName}" class="btn btn-success btn-lg btn-block">
                            <i class="fas fa-download"></i><br>
                            <small>Download Video</small>
                        </a>
                    </div>
                </div>
            </div>
        `;
    } else if (['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma'].includes(fileExtension)) {
        // Audio files
        previewHtml = `
            <div class="file-preview-options text-center">
                <h5><i class="fas fa-music text-success"></i> Audio File</h5>
                <p class="text-muted mb-4">Audio player with download option:</p>

                <div class="audio-preview-container mb-4">
                    <audio controls class="w-100" style="max-width: 500px;">
                        <source src="${filePath}" type="audio/${fileExtension}">
                        Your browser does not support the audio tag.
                    </audio>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="${absoluteFileUrl}" target="_blank" class="btn btn-primary btn-lg btn-block">
                            <i class="fas fa-external-link-alt"></i><br>
                            <small>Open in New Tab</small>
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="${filePath}" download="${fileName}" class="btn btn-success btn-lg btn-block">
                            <i class="fas fa-download"></i><br>
                            <small>Download Audio</small>
                        </a>
                    </div>
                </div>
            </div>
        `;
    } else if (['txt', 'md', 'json', 'xml', 'csv', 'log', 'js', 'css', 'html', 'htm', 'php', 'py', 'java', 'cpp', 'c', 'h', 'cs', 'rb', 'go', 'rs', 'swift', 'kt', 'ts', 'jsx', 'vue', 'sql', 'yaml', 'yml', 'ini', 'cfg', 'conf'].includes(fileExtension)) {
        // Text files - handle based on size
        const fileSizeMB = fileSize ? (fileSize / (1024 * 1024)).toFixed(2) : 'Unknown';
        const isLargeTextFile = fileSize && fileSize > (100 * 1024 * 1024); // 100MB threshold

        if (isLargeTextFile) {
            // Large text file - show options
            previewHtml = `
                <div class="file-preview-options text-center">
                    <h5><i class="fas fa-file-alt text-warning"></i> Large Text File</h5>
                    <p class="text-muted mb-3">This text file is ${fileSizeMB} MB. Full preview may be slow.</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Large File Notice:</strong> Only the first portion will be shown for preview.
                    </div>
                    <div class="mt-3">
                        <button onclick="loadLargeTextFile('${filePath}', '${fileName}')" class="btn btn-primary btn-lg">
                            <i class="fas fa-eye"></i> Load Preview (First 1MB)
                        </button>
                        <a href="${absoluteFileUrl}" target="_blank" class="btn btn-secondary btn-lg ms-2">
                            <i class="fas fa-external-link-alt"></i> Open in New Tab
                        </a>
                        <a href="${filePath}" download="${fileName}" class="btn btn-success btn-lg ms-2">
                            <i class="fas fa-download"></i> Download File
                        </a>
                    </div>
                </div>
            `;
        } else {
            // Regular text file - try to load content
            previewHtml = `
                <div class="file-preview-loading text-center">
                    <div class="spinner-border text-primary" role="status"></div>
                    <p>Loading text content...</p>
                </div>
            `;

            // Load text content asynchronously
            setTimeout(() => {
                fetch(filePath)
                    .then(response => response.text())
                    .then(content => {
                        const textPreview = `
                            <div class="file-preview-text">
                                <pre class="bg-light p-3 border rounded" style="max-height: 60vh; overflow-y: auto; white-space: pre-wrap; font-size: 0.9rem;">${content}</pre>
                            </div>
                        `;
                        document.getElementById('filePreviewContent').innerHTML = fileInfo + textPreview;
                    })
                .catch(error => {
                    console.error('Error loading text file:', error);
                    const errorPreview = `
                        <div class="file-preview-error">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h5>Unable to load text content</h5>
                            <p>The text file could not be loaded for preview. This may be due to:</p>
                            <ul class="text-left" style="display: inline-block;">
                                <li>File size too large</li>
                                <li>Network connectivity issues</li>
                                <li>Server timeout</li>
                                <li>File encoding issues</li>
                            </ul>
                            <div class="mt-3">
                                <a href="${absoluteFileUrl}" target="_blank" class="btn btn-primary">
                                    <i class="fas fa-external-link-alt"></i> Open in New Tab
                                </a>
                                <a href="${filePath}" download="${fileName}" class="btn btn-success ms-2">
                                    <i class="fas fa-download"></i> Download File
                                </a>
                            </div>
                        </div>
                    `;
                    document.getElementById('filePreviewContent').innerHTML = fileInfo + errorPreview;
                });
        }, 500);

        // Set initial content and return early
        previewContent.innerHTML = fileInfo + previewHtml;
        return;
        }
    } else if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'exe', 'msi', 'dmg', 'pkg', 'deb', 'rpm', 'ttf', 'otf', 'woff', 'woff2', 'eot', 'db', 'sqlite', 'mdb', 'accdb', 'iso', 'img', 'bin', 'dll', 'so', 'dylib'].includes(fileExtension)) {
        // File types that cannot be previewed and should not use Google Docs Viewer
        const fileTypeInfo = {
            'zip': { icon: 'fas fa-file-archive', name: 'Archive', color: 'warning' },
            'rar': { icon: 'fas fa-file-archive', name: 'Archive', color: 'warning' },
            '7z': { icon: 'fas fa-file-archive', name: 'Archive', color: 'warning' },
            'tar': { icon: 'fas fa-file-archive', name: 'Archive', color: 'warning' },
            'gz': { icon: 'fas fa-file-archive', name: 'Archive', color: 'warning' },
            'exe': { icon: 'fas fa-cog', name: 'Executable', color: 'danger' },
            'msi': { icon: 'fas fa-cog', name: 'Installer', color: 'danger' },
            'dmg': { icon: 'fas fa-cog', name: 'Disk Image', color: 'danger' },
            'ttf': { icon: 'fas fa-font', name: 'Font', color: 'info' },
            'otf': { icon: 'fas fa-font', name: 'Font', color: 'info' },
            'woff': { icon: 'fas fa-font', name: 'Web Font', color: 'info' },
            'db': { icon: 'fas fa-database', name: 'Database', color: 'secondary' },
            'sqlite': { icon: 'fas fa-database', name: 'Database', color: 'secondary' },
            'iso': { icon: 'fas fa-compact-disc', name: 'Disk Image', color: 'primary' }
        };

        const typeInfo = fileTypeInfo[fileExtension] || { icon: 'fas fa-file', name: 'Binary', color: 'secondary' };

        previewHtml = `
            <div class="file-preview-options text-center">
                <h5><i class="${typeInfo.icon} text-${typeInfo.color}"></i> ${typeInfo.name} File</h5>
                <p class="text-muted mb-4">This ${typeInfo.name.toLowerCase()} file cannot be previewed in the browser.</p>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>File Type:</strong> ${fileExtension.toUpperCase()} files need to be downloaded and opened with appropriate software.
                </div>

                <div class="row justify-content-center">
                    <div class="col-md-6 mb-3">
                        <a href="${filePath}" download="${fileName}" class="btn btn-success btn-lg btn-block">
                            <i class="fas fa-download"></i><br>
                            <small>Download File</small>
                        </a>
                    </div>
                </div>
            </div>
        `;
    } else if (['rtf', 'odt', 'ods', 'odp', 'pages', 'numbers', 'key'].includes(fileExtension)) {
        // Document file types that might work with Google Docs Viewer
        previewHtml = `
            <div class="file-preview-options text-center">
                <h5><i class="fas fa-file-alt text-primary"></i> Document File</h5>
                <p class="text-muted mb-4">Choose how you'd like to view this document:</p>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="${absoluteFileUrl}" target="_blank" class="btn btn-primary btn-lg btn-block">
                            <i class="fas fa-external-link-alt"></i><br>
                            <small>Open in New Tab</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="https://docs.google.com/viewer?url=${encodedFileUrl}&embedded=true" target="_blank" class="btn btn-info btn-lg btn-block">
                            <i class="fas fa-eye"></i><br>
                            <small>Google Docs Viewer</small>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="${filePath}" download="${fileName}" class="btn btn-success btn-lg btn-block">
                            <i class="fas fa-download"></i><br>
                            <small>Download File</small>
                        </a>
                    </div>
                </div>
            </div>
        `;
    } else {
        // Unknown file types - provide basic options without Google Docs Viewer
        previewHtml = `
            <div class="file-preview-options text-center">
                <h5><i class="fas fa-file"></i> ${fileExtension.toUpperCase()} File</h5>
                <p class="text-muted mb-4">This file type is not recognized. Choose an option below:</p>

                <div class="row justify-content-center">
                    <div class="col-md-6 mb-3">
                        <a href="${absoluteFileUrl}" target="_blank" class="btn btn-primary btn-lg btn-block">
                            <i class="fas fa-external-link-alt"></i><br>
                            <small>Open in New Tab</small>
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="${filePath}" download="${fileName}" class="btn btn-success btn-lg btn-block">
                            <i class="fas fa-download"></i><br>
                            <small>Download File</small>
                        </a>
                    </div>
                </div>
            </div>
        `;
    }

    // Set the final content
    previewContent.innerHTML = fileInfo + previewHtml;

    // For regular images, try to load them after setting the HTML with enhanced path handling
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(fileExtension)) {
        // Check file size again for the loading logic
        const fileSizeMB = fileSize ? (fileSize / (1024 * 1024)).toFixed(2) : 'Unknown';
        const isVeryLargeImage = fileSize && fileSize > (500 * 1024 * 1024); // 500MB threshold
        const isLargeImage = fileSize && fileSize > (100 * 1024 * 1024); // 100MB threshold

        if (!isVeryLargeImage && !isLargeImage) {
            // Add a small delay to ensure DOM is ready
            setTimeout(() => {
                const img = document.getElementById('previewImage');
                if (img) {
                    // Try multiple path variations to handle different server configurations
                    loadImageWithFallback(img, filePath, fileName);
                }
            }, 100);
        }
    }
}

// Function to load large images with user confirmation
function loadLargeImage(filePath, fileName) {
    const previewContent = document.getElementById('filePreviewContent');

    // Show loading state
    previewContent.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status"></div>
            <p class="mt-2">Loading large image... This may take a while.</p>
            <p class="text-muted">File: ${fileName}</p>
            <button onclick="cancelImageLoad()" class="btn btn-secondary mt-2">
                <i class="fas fa-times"></i> Cancel
            </button>
        </div>
    `;

    // Create image element with timeout
    const img = new Image();
    let loadTimeout;

    img.onload = function() {
        clearTimeout(loadTimeout);
        previewContent.innerHTML = `
            <div class="text-center">
                <img src="${filePath}" class="img-fluid" style="max-height: 70vh;" alt="${fileName}">
                <div class="mt-3">
                    <a href="${filePath}" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Open in New Tab
                    </a>
                    <a href="${filePath}" download="${fileName}" class="btn btn-success ms-2">
                        <i class="fas fa-download"></i> Download
                    </a>
                </div>
            </div>
        `;
    };

    img.onerror = function() {
        clearTimeout(loadTimeout);
        previewContent.innerHTML = `
            <div class="file-preview-error text-center">
                <i class="fas fa-exclamation-triangle"></i>
                <h5>Failed to load large image</h5>
                <p>The image could not be loaded. This may be due to:</p>
                <ul class="text-left" style="display: inline-block;">
                    <li>File size too large for browser</li>
                    <li>Network timeout</li>
                    <li>Server memory limitations</li>
                    <li>File corruption</li>
                </ul>
                <div class="mt-3">
                    <a href="${filePath}" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Try Opening in New Tab
                    </a>
                    <a href="${filePath}" download="${fileName}" class="btn btn-success ms-2">
                        <i class="fas fa-download"></i> Download File
                    </a>
                </div>
            </div>
        `;
    };

    // Set timeout for large image loading (60 seconds)
    loadTimeout = setTimeout(() => {
        previewContent.innerHTML = `
            <div class="preview-timeout-warning text-center">
                <i class="fas fa-clock"></i>
                <h5>Loading timeout</h5>
                <p>The image is taking too long to load. You can:</p>
                <div class="mt-3">
                    <a href="${filePath}" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Open in New Tab
                    </a>
                    <a href="${filePath}" download="${fileName}" class="btn btn-success ms-2">
                        <i class="fas fa-download"></i> Download File
                    </a>
                    <button onclick="loadLargeImage('${filePath}', '${fileName}')" class="btn btn-warning ms-2">
                        <i class="fas fa-redo"></i> Try Again
                    </button>
                </div>
            </div>
        `;
    }, 60000); // 60 second timeout

    // Start loading the image
    img.src = filePath;
}

// Function to cancel image loading
function cancelImageLoad() {
    const previewContent = document.getElementById('filePreviewContent');
    previewContent.innerHTML = `
        <div class="text-center">
            <i class="fas fa-times-circle text-muted fa-3x"></i>
            <h5 class="mt-3">Loading cancelled</h5>
            <p class="text-muted">Image loading was cancelled by user.</p>
        </div>
    `;
}

// Function to load large text files with chunked reading
function loadLargeTextFile(filePath, fileName) {
    const previewContent = document.getElementById('filePreviewContent');

    // Show loading state
    previewContent.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status"></div>
            <p class="mt-2">Loading large text file... This may take a while.</p>
            <p class="text-muted">File: ${fileName}</p>
            <p class="text-muted">Loading first 1MB for preview...</p>
            <button onclick="cancelTextLoad()" class="btn btn-secondary mt-2">
                <i class="fas fa-times"></i> Cancel
            </button>
        </div>
    `;

    // Set timeout for loading (2 minutes for large text files)
    const loadTimeout = setTimeout(() => {
        previewContent.innerHTML = `
            <div class="preview-timeout-warning text-center">
                <i class="fas fa-clock"></i>
                <h5>Loading timeout</h5>
                <p>The text file is taking too long to load. You can:</p>
                <div class="mt-3">
                    <a href="${filePath}" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Open in New Tab
                    </a>
                    <a href="${filePath}" download="${fileName}" class="btn btn-success ms-2">
                        <i class="fas fa-download"></i> Download File
                    </a>
                    <button onclick="loadLargeTextFile('${filePath}', '${fileName}')" class="btn btn-warning ms-2">
                        <i class="fas fa-redo"></i> Try Again
                    </button>
                </div>
            </div>
        `;
    }, 120000); // 2 minute timeout

    // Load text content
    fetch(filePath)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(content => {
            clearTimeout(loadTimeout);

            // Check if content was truncated (approximate check)
            const isTruncated = content.length >= (1024 * 1024 * 0.95); // ~95% of 1MB

            const textPreview = `
                <div class="file-preview-text">
                    ${isTruncated ? `
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle"></i>
                            <strong>Partial Preview:</strong> Showing first ~1MB of the file. Download for complete content.
                        </div>
                    ` : ''}
                    <pre class="bg-light p-3 border rounded" style="max-height: 60vh; overflow-y: auto; white-space: pre-wrap; font-size: 0.9rem;">${content}</pre>
                    <div class="mt-3 text-center">
                        <a href="${filePath}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt"></i> Open Full File in New Tab
                        </a>
                        <a href="${filePath}" download="${fileName}" class="btn btn-success ms-2">
                            <i class="fas fa-download"></i> Download Complete File
                        </a>
                    </div>
                </div>
            `;

            previewContent.innerHTML = textPreview;
        })
        .catch(error => {
            clearTimeout(loadTimeout);
            console.error('Error loading large text file:', error);

            previewContent.innerHTML = `
                <div class="file-preview-error text-center">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h5>Failed to load text file</h5>
                    <p>The text file could not be loaded. This may be due to:</p>
                    <ul class="text-left" style="display: inline-block;">
                        <li>File size too large for browser</li>
                        <li>Network timeout or connectivity issues</li>
                        <li>Server memory limitations</li>
                        <li>File encoding issues</li>
                    </ul>
                    <div class="mt-3">
                        <a href="${filePath}" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt"></i> Try Opening in New Tab
                        </a>
                        <a href="${filePath}" download="${fileName}" class="btn btn-success ms-2">
                            <i class="fas fa-download"></i> Download File
                        </a>
                    </div>
                </div>
            `;
        });
}

// Function to cancel text loading
function cancelTextLoad() {
    const previewContent = document.getElementById('filePreviewContent');
    previewContent.innerHTML = `
        <div class="text-center">
            <i class="fas fa-times-circle text-muted fa-3x"></i>
            <h5 class="mt-3">Loading cancelled</h5>
            <p class="text-muted">Text file loading was cancelled by user.</p>
        </div>
    `;
}

// Enhanced error handling functions for material viewing issues

// Function to retry file access
function retryFileAccess(filePath, fileName, fileSize) {
    console.log('Retrying file access for:', fileName);
    openFilePreview(filePath, fileName, fileSize);
}

// Function to retry image loading
function retryImageLoad(filePath, fileName) {
    console.log('Retrying image load for:', fileName);
    const imageLoading = document.getElementById('imageLoading');
    const imageError = document.getElementById('imageError');
    const previewImage = document.getElementById('previewImage');

    if (imageLoading) imageLoading.style.display = 'block';
    if (imageError) imageError.style.display = 'none';
    if (previewImage) {
        previewImage.style.display = 'none';
        previewImage.src = filePath + '?t=' + Date.now(); // Add timestamp to bypass cache
    }
}

// Function to open diagnostics for problematic files
function openDiagnostics(fileName) {
    const diagnosticsUrl = fileName ?
        'material-diagnostics.html?file=' + encodeURIComponent(fileName) :
        'material-diagnostics.html';
    window.open(diagnosticsUrl, '_blank', 'width=1000,height=700,scrollbars=yes,resizable=yes');
}

// Function to show general diagnostics link
function showDiagnosticsLink() {
    return `
        <div class="mt-3 text-center">
            <button onclick="openDiagnostics()" class="btn btn-info btn-sm">
                <i class="fas fa-tools"></i> Open Material Diagnostics
            </button>
            <small class="text-muted d-block mt-1">
                Use this tool to troubleshoot file viewing issues
            </small>
        </div>
    `;
}

// Function to test file accessibility
async function testFileAccessibility(filePath) {
    try {
        const response = await fetch(filePath, {
            method: 'HEAD',
            cache: 'no-cache'
        });
        return {
            accessible: response.ok,
            status: response.status,
            statusText: response.statusText,
            contentType: response.headers.get('content-type'),
            contentLength: response.headers.get('content-length')
        };
    } catch (error) {
        console.error('File accessibility test failed:', error);
        return {
            accessible: false,
            error: error.message
        };
    }
}

// Function to show detailed error information
function showDetailedError(filePath, fileName, error) {
    const previewContent = document.getElementById('filePreviewContent');
    const timestamp = new Date().toLocaleString();

    previewContent.innerHTML = `
        <div class="file-preview-error">
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> File Loading Error</h5>
                <p><strong>File:</strong> ${fileName}</p>
                <p><strong>Path:</strong> ${filePath}</p>
                <p><strong>Error:</strong> ${error}</p>
                <p><strong>Time:</strong> ${timestamp}</p>
            </div>

            <div class="troubleshooting-steps">
                <h6><i class="fas fa-tools"></i> Troubleshooting Steps:</h6>
                <ol class="text-left">
                    <li>Check your internet connection</li>
                    <li>Try refreshing the page</li>
                    <li>Clear your browser cache</li>
                    <li>Try downloading the file instead</li>
                    <li>Use the diagnostics tool below to identify the issue</li>
                    <li>Contact your administrator if the problem persists</li>
                </ol>
            </div>

            <div class="mt-3">
                <button onclick="retryFileAccess('${filePath}', '${fileName}', 0)" class="btn btn-primary">
                    <i class="fas fa-redo"></i> Retry
                </button>
                <a href="${filePath}" target="_blank" class="btn btn-info ms-2">
                    <i class="fas fa-external-link-alt"></i> Open in New Tab
                </a>
                <a href="${filePath}" download="${fileName}" class="btn btn-success ms-2">
                    <i class="fas fa-download"></i> Download
                </a>
                <button onclick="openDiagnostics('${fileName}')" class="btn btn-warning ms-2">
                    <i class="fas fa-tools"></i> Diagnostics
                </button>
            </div>

            ${showDiagnosticsLink()}
        </div>
    `;
}

// Function to handle network errors specifically
function handleNetworkError(filePath, fileName) {
    showDetailedError(filePath, fileName, 'Network connectivity issue - unable to reach the file server');
}

// Function to handle file not found errors
function handleFileNotFound(filePath, fileName) {
    showDetailedError(filePath, fileName, 'File not found on server - the file may have been moved or deleted');
}

// Function to handle permission errors
function handlePermissionError(filePath, fileName) {
    showDetailedError(filePath, fileName, 'Access denied - you may not have permission to view this file');
}

// Function to show detailed error information for file viewing issues
function showFileViewingHelp(fileName, fileSize) {
    const fileSizeMB = fileSize ? (fileSize / (1024 * 1024)).toFixed(2) : 'Unknown';

    return `
        <div class="file-preview-error">
            <i class="fas fa-exclamation-triangle"></i>
            <h5>File Viewing Issues</h5>
            <p><strong>File:</strong> ${fileName} (${fileSizeMB} MB)</p>

            <div class="accordion" id="troubleshootingAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#commonIssues">
                            Common Issues & Solutions
                        </button>
                    </h2>
                    <div id="commonIssues" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                        <div class="accordion-body">
                            <ul class="text-left">
                                <li><strong>Large File Size:</strong> Files over 50MB may load slowly or fail in some browsers</li>
                                <li><strong>Network Issues:</strong> Slow or unstable internet connection</li>
                                <li><strong>Browser Limitations:</strong> Some browsers have memory limits for large files</li>
                                <li><strong>File Corruption:</strong> The file may be damaged or incomplete</li>
                                <li><strong>Server Timeout:</strong> Server may timeout for very large files</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#recommendations">
                            Recommended Actions
                        </button>
                    </h2>
                    <div id="recommendations" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                        <div class="accordion-body">
                            <ol class="text-left">
                                <li>Try opening the file in a new tab (less memory intensive)</li>
                                <li>Download the file and open it locally</li>
                                <li>Try using a different browser (Chrome, Firefox, Edge)</li>
                                <li>Check your internet connection</li>
                                <li>Try again later when the server is less busy</li>
                                <li>Contact your teacher if the problem persists</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Enhanced error handling for classroom data loading
function handleClassroomLoadError(error) {
    console.error("Error loading classroom data:", error);

    let errorMessage = 'Error loading classroom data. ';
    let troubleshooting = '';

    if (error.code === 'permission-denied') {
        errorMessage += 'You do not have permission to access this classroom.';
        troubleshooting = `
            <div class="mt-3">
                <h6>Possible Solutions:</h6>
                <ul>
                    <li>Make sure you are logged in with the correct account</li>
                    <li>Check that you are enrolled in this classroom</li>
                    <li>Contact your teacher to verify your enrollment</li>
                </ul>
            </div>
        `;
    } else if (error.code === 'not-found') {
        errorMessage += 'The classroom could not be found.';
        troubleshooting = `
            <div class="mt-3">
                <h6>Possible Solutions:</h6>
                <ul>
                    <li>Check that the classroom URL is correct</li>
                    <li>The classroom may have been deleted or moved</li>
                    <li>Contact your teacher for the correct classroom link</li>
                </ul>
            </div>
        `;
    } else if (error.code === 'unavailable') {
        errorMessage += 'The service is currently unavailable. Please try again later.';
        troubleshooting = `
            <div class="mt-3">
                <h6>Possible Solutions:</h6>
                <ul>
                    <li>Wait a few minutes and refresh the page</li>
                    <li>Check your internet connection</li>
                    <li>Try accessing from a different device or network</li>
                </ul>
            </div>
        `;
    } else {
        errorMessage += 'Please try again later.';
        troubleshooting = `
            <div class="mt-3">
                <h6>Troubleshooting Steps:</h6>
                <ul>
                    <li>Refresh the page (Ctrl+F5 or Cmd+Shift+R)</li>
                    <li>Clear your browser cache and cookies</li>
                    <li>Try using a different browser</li>
                    <li>Check your internet connection</li>
                    <li>Contact technical support if the problem persists</li>
                </ul>
            </div>
        `;
    }

    document.getElementById('materials-list').innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Error:</strong> ${errorMessage}
            ${troubleshooting}
            <div class="mt-3">
                <button onclick="location.reload()" class="btn btn-primary btn-sm">
                    <i class="fas fa-redo"></i> Retry
                </button>
                <button onclick="window.history.back()" class="btn btn-secondary btn-sm ms-2">
                    <i class="fas fa-arrow-left"></i> Go Back
                </button>
            </div>
        </div>
    `;
}

// Enhanced image loading functions
function handleImageLoad(imgElement) {
    const imageLoading = document.getElementById('imageLoading');
    if (imageLoading) imageLoading.style.display = 'none';
    imgElement.style.display = 'block';
    console.log('Image loaded successfully:', imgElement.src);
}

function handleImageError(filePath, fileName) {
    console.error('Image failed to load:', filePath);
    const imageLoading = document.getElementById('imageLoading');
    const imageError = document.getElementById('imageError');

    if (imageLoading) imageLoading.style.display = 'none';
    if (imageError) imageError.style.display = 'block';
}

function loadImageWithFallback(imgElement, originalPath, fileName) {
    const pathVariations = [
        originalPath,
        './' + originalPath,
        window.location.origin + '/' + originalPath,
        originalPath.replace(/^\/+/, ''), // Remove leading slashes
        'uploads/' + fileName // Direct uploads path
    ];

    let currentIndex = 0;

    function tryNextPath() {
        if (currentIndex >= pathVariations.length) {
            console.error('All image path variations failed for:', fileName);
            handleImageError(originalPath, fileName);
            return;
        }

        const currentPath = pathVariations[currentIndex];
        console.log(`Trying image path ${currentIndex + 1}/${pathVariations.length}:`, currentPath);

        // Create a test image to check if the path works
        const testImg = new Image();

        testImg.onload = function() {
            console.log('Image path successful:', currentPath);
            imgElement.src = currentPath;
            handleImageLoad(imgElement);
        };

        testImg.onerror = function() {
            console.log('Image path failed:', currentPath);
            currentIndex++;
            tryNextPath();
        };

        // Set a timeout for each attempt
        setTimeout(() => {
            if (!testImg.complete) {
                console.log('Image path timeout:', currentPath);
                currentIndex++;
                tryNextPath();
            }
        }, 3000);

        testImg.src = currentPath;
    }

    tryNextPath();
}

function testImagePath(filePath, fileName) {
    console.log('Testing image path:', filePath);

    // Test if the file exists
    fetch(filePath, { method: 'HEAD' })
        .then(response => {
            if (response.ok) {
                alert(`✅ File accessible!\nPath: ${filePath}\nStatus: ${response.status} ${response.statusText}`);
            } else {
                alert(`❌ File not accessible!\nPath: ${filePath}\nStatus: ${response.status} ${response.statusText}`);
            }
        })
        .catch(error => {
            alert(`❌ Network error!\nPath: ${filePath}\nError: ${error.message}`);
        });
}