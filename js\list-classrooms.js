// Import Firebase modules
import { collection, getDocs, doc, updateDoc, arrayUnion } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js";

// Use the globally initialized Firebase instance
let db;

// Wait for the Firebase instance to be available
function getFirestoreInstance() {
    return new Promise((resolve, reject) => {
        // Check if db is already available in the window object
        if (window.db) {
            console.log("Using existing Firestore instance from window object in list-classrooms.js");
            resolve(window.db);
            return;
        }

        // If not available, wait for it with a timeout
        let attempts = 0;
        const maxAttempts = 10;
        const checkInterval = setInterval(() => {
            attempts++;
            if (window.db) {
                clearInterval(checkInterval);
                console.log("Firestore instance found after waiting in list-classrooms.js");
                resolve(window.db);
            } else if (attempts >= maxAttempts) {
                clearInterval(checkInterval);
                reject(new Error("Firestore instance not available after maximum attempts"));
            }
        }, 300);
    });
}

// Initialize db when the module loads
getFirestoreInstance()
    .then(firestoreInstance => {
        db = firestoreInstance;
        console.log("Firestore instance set in list-classrooms.js");
    })
    .catch(error => {
        console.error("Error getting Firestore instance in list-classrooms.js:", error);
    });

// Function to display classrooms in a list format
// Make it available globally for the refresh button
window.displayClassrooms = async function() {
    try {
        // Make sure we have a Firestore instance
        if (!db) {
            console.log("Waiting for Firestore instance in window.displayClassrooms...");
            try {
                db = await getFirestoreInstance();
                console.log("Firestore instance obtained for window.displayClassrooms");
            } catch (error) {
                console.error("Failed to get Firestore instance:", error);
                throw error;
            }
        }

        const querySnapshot = await getDocs(collection(db, "Classrooms"));
        const classroomContainer = document.getElementById('classroom-list');

        if (querySnapshot.empty) {
            classroomContainer.innerHTML = '<p class="text-center">No classrooms found.</p>';
            return;
        }

        let html = '<div class="list-group">';

        querySnapshot.forEach((doc) => {
            const classroom = doc.data();
            const classroomId = doc.id;

            html += `
                <div class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h5 class="mb-1 subject-name-text" style="color: #000 !important; font-weight: normal !important;">${classroom.subjectName}</h5>
                        <small style="color: black;">${classroom.enrollCode}</small>
                    </div>
                    <p class="mb-1" style="color: black;">${classroom.details}</p>
                    <small style="color: black;">${classroom.gradeLevel}</small>
                    <small style="color: black; display: block;">Section: ${classroom.sectionName || 'No Section'}</small>
                    <small style="color: black; display: block;">Strand: ${classroom.course}</small>
                    <small style="color: black; display: block; margin-top: 5px;">Created by: ${classroom.createdBy ? (classroom.creatorName || classroom.createdBy) : 'Unknown'}</small>
                    <div class="mt-2">
                        <a href="classroom-materials.html?id=${classroomId}" class="btn btn-sm btn-info mr-2" onclick="if(window.isNavigatingWithinLMS !== undefined) window.isNavigatingWithinLMS = true;">
                            <i class="fas fa-book"></i> View Materials
                        </a>

                    </div>
                </div>
            `;
        });

        html += '</div>';
        classroomContainer.innerHTML = html;

        // Add event listeners to post material buttons
        document.querySelectorAll('.post-material-btn').forEach(button => {
            button.addEventListener('click', function() {
                const classroomId = this.getAttribute('data-classroom-id');
                const classroomName = this.getAttribute('data-classroom-name');
                openPostMaterialModal(classroomId, classroomName);
            });
        });
    } catch (error) {
        console.error("Error fetching classrooms:", error);
        document.getElementById('classroom-list').innerHTML =
            '<p class="text-center text-danger">Error loading classrooms. Please try again later.</p>';
    }
}

// Function to open the post material modal
function openPostMaterialModal(classroomId, classroomName) {
    // Create modal if it doesn't exist
    if (!document.getElementById('postMaterialModal')) {
        const modalHtml = `
            <div class="modal fade" id="postMaterialModal" tabindex="-1" role="dialog" aria-labelledby="postMaterialModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="postMaterialModalLabel">Post Material to <span id="modal-classroom-name"></span></h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="post-material-form">
                                <input type="hidden" id="classroom-id-input">
                                <div class="form-group">
                                    <label for="material-title">Material Title</label>
                                    <input type="text" class="form-control" id="material-title" required>
                                </div>
                                <div class="form-group">
                                    <label for="material-description">Description</label>
                                    <textarea class="form-control" id="material-description" rows="3"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="material-file">File</label>
                                    <input type="file" class="form-control-file" id="material-file">
                                </div>
                                <div class="form-group">
                                    <label for="material-link">Link (optional)</label>
                                    <input type="url" class="form-control" id="material-link">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="submit-material-btn">Post Material</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to the document
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Add event listener to submit button
        document.getElementById('submit-material-btn').addEventListener('click', postMaterial);
    }

    // Set classroom info in the modal
    document.getElementById('modal-classroom-name').textContent = classroomName;
    document.getElementById('classroom-id-input').value = classroomId;

    // Show the modal
    $('#postMaterialModal').modal('show');
}

// Function to post material to a classroom
async function postMaterial() {
    const classroomId = document.getElementById('classroom-id-input').value;
    const title = document.getElementById('material-title').value;
    const description = document.getElementById('material-description').value;
    const fileInput = document.getElementById('material-file');
    const link = document.getElementById('material-link').value;

    if (!title) {
        alert('Please enter a material title');
        return;
    }

    try {
        // Create FormData object for file upload
        const formData = new FormData();
        formData.append('classroomId', classroomId);
        formData.append('title', title);
        formData.append('description', description);
        formData.append('link', link);

        // Add file if selected
        if (fileInput.files.length > 0) {
            formData.append('file', fileInput.files[0]);
        }

        // Send the request to PHP endpoint
        const response = await fetch('upload_material.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || 'Failed to upload material');
        }

        // Close the modal
        $('#postMaterialModal').modal('hide');

        // Reset the form
        document.getElementById('post-material-form').reset();

        // Show success message
        alert('Material posted successfully!');

        // Redirect to the classroom materials page
        window.location.href = `classroom-materials.html?id=${classroomId}`;
    } catch (error) {
        console.error("Error posting material:", error);
        alert('Failed to post material: ' + error.message);
    }
}

// Call the function when the page loads
document.addEventListener('DOMContentLoaded', displayClassrooms);

// Listen for refresh event
document.addEventListener('refreshClassroomList', function() {
    console.log('Refresh classroom list event received');
    displayClassrooms().then(() => {
        if (typeof window.refreshClassroomList === 'function') {
            window.refreshClassroomList();
        }
    }).catch(error => {
        console.error('Error refreshing classroom list:', error);
        if (typeof window.refreshClassroomList === 'function') {
            window.refreshClassroomList();
        }
    });
});